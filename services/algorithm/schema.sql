-- Database schema for Algorithm Service
-- This script creates the required tables for IRT and BKT parameters

-- Create models schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS models;

-- IRT Parameters Table
CREATE TABLE IF NOT EXISTS models.item_response_theory_parameters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_version_id UUID NOT NULL UNIQUE,
    difficulty DECIMAL(10, 6) NOT NULL,
    discrimination DECIMAL(10, 6) NOT NULL,
    guessing DECIMAL(10, 6) DEFAULT 0.0,
    model_type VARCHAR(10) NOT NULL DEFAULT '2PL',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT irt_model_type_check CHECK (model_type IN ('1PL', '2PL', '3PL')),
    CONSTRAINT irt_difficulty_range CHECK (difficulty BETWEEN -10.0 AND 10.0),
    CONSTRAINT irt_discrimination_positive CHECK (discrimination > 0),
    CONSTRAINT irt_guessing_range CHECK (guessing BETWEEN 0.0 AND 1.0)
);

-- BKT Parameters Table
CREATE TABLE IF NOT EXISTS models.bayesian_knowledge_tracing_parameters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    skill_id VARCHAR(255) NOT NULL UNIQUE,
    p_initial DECIMAL(10, 6) NOT NULL,
    p_learn DECIMAL(10, 6) NOT NULL,
    p_guess DECIMAL(10, 6) NOT NULL,
    p_slip DECIMAL(10, 6) NOT NULL,
    p_forget DECIMAL(10, 6) DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT bkt_p_initial_range CHECK (p_initial BETWEEN 0.0 AND 1.0),
    CONSTRAINT bkt_p_learn_range CHECK (p_learn BETWEEN 0.0 AND 1.0),
    CONSTRAINT bkt_p_guess_range CHECK (p_guess BETWEEN 0.0 AND 1.0),
    CONSTRAINT bkt_p_slip_range CHECK (p_slip BETWEEN 0.0 AND 1.0),
    CONSTRAINT bkt_p_forget_range CHECK (p_forget IS NULL OR (p_forget BETWEEN 0.0 AND 1.0))
);

-- Test Template Skills Table (for mapping test templates to skills)
CREATE TABLE IF NOT EXISTS models.test_template_skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_template_id UUID NOT NULL,
    skill_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(test_template_id, skill_id)
);

-- Question Skills Table (for mapping questions to skills they assess)
CREATE TABLE IF NOT EXISTS models.question_skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_version_id UUID NOT NULL,
    skill_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(question_version_id, skill_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_irt_params_question_version ON models.item_response_theory_parameters(question_version_id);
CREATE INDEX IF NOT EXISTS idx_bkt_params_skill ON models.bayesian_knowledge_tracing_parameters(skill_id);
CREATE INDEX IF NOT EXISTS idx_test_template_skills_template ON models.test_template_skills(test_template_id);
CREATE INDEX IF NOT EXISTS idx_test_template_skills_skill ON models.test_template_skills(skill_id);
CREATE INDEX IF NOT EXISTS idx_question_skills_question ON models.question_skills(question_version_id);
CREATE INDEX IF NOT EXISTS idx_question_skills_skill ON models.question_skills(skill_id);

-- Insert sample data for testing
INSERT INTO models.bayesian_knowledge_tracing_parameters (skill_id, p_initial, p_learn, p_guess, p_slip, p_forget)
VALUES
    ('math_algebra', 0.2, 0.3, 0.25, 0.1, NULL),
    ('math_equations', 0.15, 0.35, 0.2, 0.12, NULL),
    ('math_fractions', 0.25, 0.28, 0.3, 0.08, NULL),
    ('reading_comprehension', 0.3, 0.25, 0.15, 0.1, NULL),
    ('vocabulary', 0.4, 0.2, 0.1, 0.05, NULL);

-- Insert sample IRT parameters
INSERT INTO models.item_response_theory_parameters (question_version_id, difficulty, discrimination, guessing, model_type)
VALUES
    ('11111111-1111-1111-1111-111111111111', 0.5, 1.2, 0.2, '2PL'),
    ('22222222-2222-2222-2222-222222222222', -0.3, 1.5, 0.25, '2PL'),
    ('33333333-3333-3333-3333-333333333333', 1.2, 0.8, 0.15, '2PL'),
    ('44444444-4444-4444-4444-444444444444', -1.0, 1.8, 0.3, '2PL'),
    ('55555555-5555-5555-5555-555555555555', 0.0, 1.0, 0.2, '2PL');

-- Insert sample test template skills mapping
INSERT INTO models.test_template_skills (test_template_id, skill_id)
VALUES
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'math_algebra'),
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'math_equations'),
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'reading_comprehension'),
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'vocabulary'),
    ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'math_fractions');

-- Insert sample question skills mapping
INSERT INTO models.question_skills (question_version_id, skill_id)
VALUES
    ('11111111-1111-1111-1111-111111111111', 'math_algebra'),
    ('22222222-2222-2222-2222-222222222222', 'math_equations'),
    ('33333333-3333-3333-3333-333333333333', 'math_fractions'),
    ('44444444-4444-4444-4444-444444444444', 'reading_comprehension'),
    ('55555555-5555-5555-5555-555555555555', 'vocabulary');

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update the updated_at column
CREATE TRIGGER update_irt_params_updated_at 
    BEFORE UPDATE ON models.item_response_theory_parameters 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bkt_params_updated_at 
    BEFORE UPDATE ON models.bayesian_knowledge_tracing_parameters 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
