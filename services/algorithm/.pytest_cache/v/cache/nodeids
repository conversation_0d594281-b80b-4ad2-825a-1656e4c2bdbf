["tests/test_api.py::TestHealthEndpoint::test_health_check_healthy", "tests/test_api.py::TestHealthEndpoint::test_health_check_unhealthy", "tests/test_api.py::TestMetricsEndpoint::test_metrics_endpoint", "tests/test_api.py::TestSessionEndpoints::test_initialize_session_invalid_request", "tests/test_api.py::TestSessionEndpoints::test_initialize_session_service_error", "tests/test_api.py::TestSessionEndpoints::test_initialize_session_success", "tests/test_api.py::TestSessionEndpoints::test_update_session_invalid_uuid", "tests/test_api.py::TestSessionEndpoints::test_update_session_success", "tests/test_bkt_engine.py::TestBKTEngine::test_all_skills_mastered", "tests/test_bkt_engine.py::TestBKTEngine::test_calculate_overall_mastery", "tests/test_bkt_engine.py::TestBKTEngine::test_calculate_overall_mastery_empty", "tests/test_bkt_engine.py::TestBKTEngine::test_get_mastered_skills", "tests/test_bkt_engine.py::TestBKTEngine::test_get_unmastered_skills", "tests/test_bkt_engine.py::TestBKTEngine::test_get_weakest_skill", "tests/test_bkt_engine.py::TestBKTEngine::test_get_weakest_skill_empty", "tests/test_bkt_engine.py::TestBKTEngine::test_initialize_skill_mastery", "tests/test_bkt_engine.py::TestBKTEngine::test_initialize_skill_mastery_no_params", "tests/test_bkt_engine.py::TestBKTEngine::test_is_skill_mastered", "tests/test_bkt_engine.py::TestBKTEngine::test_update_multiple_skills", "tests/test_bkt_engine.py::TestBKTEngine::test_update_skill_mastery_bounds", "tests/test_bkt_engine.py::TestBKTEngine::test_update_skill_mastery_correct", "tests/test_bkt_engine.py::TestBKTEngine::test_update_skill_mastery_incorrect", "tests/test_irt_engine.py::TestIRTEngine::test_ability_precision_met", "tests/test_irt_engine.py::TestIRTEngine::test_fisher_information_2pl", "tests/test_irt_engine.py::TestIRTEngine::test_fisher_information_non_negative", "tests/test_irt_engine.py::TestIRTEngine::test_optimal_difficulty", "tests/test_irt_engine.py::TestIRTEngine::test_probability_bounds", "tests/test_irt_engine.py::TestIRTEngine::test_probability_correct_2pl", "tests/test_irt_engine.py::TestIRTEngine::test_probability_correct_3pl", "tests/test_irt_engine.py::TestIRTEngine::test_theta_bounds", "tests/test_irt_engine.py::TestIRTEngine::test_update_ability_correct_response", "tests/test_irt_engine.py::TestIRTEngine::test_update_ability_incorrect_response"]