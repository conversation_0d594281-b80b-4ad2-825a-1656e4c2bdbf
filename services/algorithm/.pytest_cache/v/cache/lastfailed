{"tests/test_irt_engine.py::TestIRTEngine::test_probability_correct_2pl": true, "tests/test_irt_engine.py::TestIRTEngine::test_fisher_information_2pl": true, "tests/test_api.py::TestHealthEndpoint::test_health_check_healthy": true, "tests/test_api.py::TestHealthEndpoint::test_health_check_unhealthy": true, "tests/test_api.py::TestSessionEndpoints::test_initialize_session_success": true, "tests/test_api.py::TestSessionEndpoints::test_initialize_session_invalid_request": true, "tests/test_api.py::TestSessionEndpoints::test_initialize_session_service_error": true, "tests/test_api.py::TestSessionEndpoints::test_update_session_success": true, "tests/test_api.py::TestSessionEndpoints::test_update_session_invalid_uuid": true, "tests/test_api.py::TestMetricsEndpoint::test_metrics_endpoint": true}