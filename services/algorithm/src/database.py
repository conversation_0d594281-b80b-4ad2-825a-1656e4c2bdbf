"""
Database connection and data access layer for the Algorithm Service
"""

import time
from typing import List, Optional, Dict, Any
from uuid import UUID
import asyncio
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2.pool import ThreadedConnectionPool
import structlog

from .models import IRTParameters, BKTParameters
from .metrics import MetricsManager

logger = structlog.get_logger(__name__)


class DatabaseManager:
    """Manages database connections and queries"""
    
    def __init__(self, connection_string: str, metrics_manager: Optional[MetricsManager] = None, min_connections: int = 1, max_connections: int = 10):
        self.connection_string = connection_string
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.pool: Optional[ThreadedConnectionPool] = None
        self.metrics = metrics_manager or MetricsManager()
    
    async def initialize(self):
        """Initialize database connection pool"""
        try:
            # Create connection pool
            self.pool = ThreadedConnectionPool(
                self.min_connections,
                self.max_connections,
                self.connection_string
            )
            
            # Test connection
            await self.health_check()
            logger.info("Database connection pool initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize database connection pool", error=str(e))
            raise
    
    async def close(self):
        """Close database connection pool"""
        if self.pool:
            self.pool.closeall()
            logger.info("Database connection pool closed")
    
    async def health_check(self) -> bool:
        """Check database connectivity"""
        try:
            conn = self.pool.getconn()
            try:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    return result is not None
            finally:
                self.pool.putconn(conn)
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False
    
    async def get_irt_parameters(self, question_version_id: UUID) -> Optional[IRTParameters]:
        """Get IRT parameters for a question"""
        start_time = time.time()
        
        try:
            conn = self.pool.getconn()
            try:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    query = """
                    SELECT difficulty, discrimination, guessing, model_type
                    FROM models.item_response_theory_parameters
                    WHERE question_version_id = %s
                    ORDER BY creation_date DESC
                    LIMIT 1
                    """
                    cursor.execute(query, (str(question_version_id),))
                    result = cursor.fetchone()
                    
                    if result:
                        return IRTParameters(
                            difficulty=result['difficulty'],
                            discrimination=result['discrimination'],
                            guessing=result['guessing'],
                            model_type=result['model_type']
                        )
                    return None
            finally:
                self.pool.putconn(conn)
                
        except Exception as e:
            logger.error(
                "Failed to get IRT parameters",
                question_version_id=str(question_version_id),
                error=str(e)
            )
            self.metrics.record_error("irt_parameters_query_failed")
            raise
        finally:
            duration = time.time() - start_time
            self.metrics.record_db_query("irt_parameters", duration)
    
    async def get_bkt_parameters(self, skill_id: str) -> Optional[BKTParameters]:
        """Get BKT parameters for a skill"""
        start_time = time.time()
        
        try:
            conn = self.pool.getconn()
            try:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    query = """
                    SELECT p_initial, p_learn, p_guess, p_slip, p_forget
                    FROM models.bayesian_knowledge_tracing_parameters
                    WHERE skill_id = %s
                    ORDER BY creation_date DESC
                    LIMIT 1
                    """
                    cursor.execute(query, (skill_id,))
                    result = cursor.fetchone()
                    
                    if result:
                        return BKTParameters(
                            p_initial=result['p_initial'],
                            p_learn=result['p_learn'],
                            p_guess=result['p_guess'],
                            p_slip=result['p_slip'],
                            p_forget=result['p_forget']
                        )
                    return None
            finally:
                self.pool.putconn(conn)
                
        except Exception as e:
            logger.error(
                "Failed to get BKT parameters",
                skill_id=skill_id,
                error=str(e)
            )
            self.metrics.record_error("bkt_parameters_query_failed")
            raise
        finally:
            duration = time.time() - start_time
            self.metrics.record_db_query("bkt_parameters", duration)
    
    async def get_skills_for_test_template(self, test_template_id: UUID) -> List[str]:
        """Get all skills associated with a test template"""
        start_time = time.time()
        
        try:
            conn = self.pool.getconn()
            try:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    query = """
                    SELECT DISTINCT s.id
                    FROM content.skills s
                    JOIN content.question_skill_mapping qsm ON s.id = qsm.skill_id
                    JOIN content.question_versions qv ON qsm.question_version_id = qv.id
                    JOIN content.questions q ON qv.question_id = q.id
                    WHERE q.subject_id IN (
                        SELECT subject_id FROM content.test_templates WHERE id = %s
                    )
                    """
                    cursor.execute(query, (str(test_template_id),))
                    results = cursor.fetchall()
                    
                    return [str(row['id']) for row in results]
            finally:
                self.pool.putconn(conn)
                
        except Exception as e:
            logger.error(
                "Failed to get skills for test template",
                test_template_id=str(test_template_id),
                error=str(e)
            )
            self.metrics.record_error("skills_query_failed")
            raise
        finally:
            duration = time.time() - start_time
            self.metrics.record_db_query("skills_for_template", duration)
    
    async def get_question_skills(self, question_version_id: UUID) -> List[str]:
        """Get skills assessed by a specific question"""
        start_time = time.time()
        
        try:
            conn = self.pool.getconn()
            try:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    query = """
                    SELECT skill_id
                    FROM content.question_skill_mapping
                    WHERE question_version_id = %s
                    """
                    cursor.execute(query, (str(question_version_id),))
                    results = cursor.fetchall()
                    
                    return [str(row['skill_id']) for row in results]
            finally:
                self.pool.putconn(conn)
                
        except Exception as e:
            logger.error(
                "Failed to get question skills",
                question_version_id=str(question_version_id),
                error=str(e)
            )
            self.metrics.record_error("question_skills_query_failed")
            raise
        finally:
            duration = time.time() - start_time
            self.metrics.record_db_query("question_skills", duration)
