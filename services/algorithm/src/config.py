"""
Configuration settings for the Algorithm Service
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings"""

    model_config = {
        "extra": "allow",
        "env_file": ".env",
        "case_sensitive": False
    }
    
    # Server configuration
    port: int = Field(default=8081, env="PORT")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Database configuration
    db_conn_string: str = Field(
        default="postgres://postgres:postgres@localhost:5432/orchestrator?sslmode=disable",
        env="DB_CONN_STRING"
    )
    
    # Algorithm configuration
    default_theta: float = Field(default=0.0, env="DEFAULT_THETA")
    default_std_error: float = Field(default=1.0, env="DEFAULT_STD_ERROR")
    max_items_per_test: int = Field(default=50, env="MAX_ITEMS_PER_TEST")
    theta_precision_threshold: float = Field(default=0.3, env="THETA_PRECISION_THRESHOLD")
    
    # BKT configuration
    default_p_initial: float = Field(default=0.2, env="DEFAULT_P_INITIAL")
    mastery_threshold: float = Field(default=0.8, env="MASTERY_THRESHOLD")
    
    # IRT configuration
    max_theta: float = Field(default=4.0, env="MAX_THETA")
    min_theta: float = Field(default=-4.0, env="MIN_THETA")
    



# Global settings instance
settings = Settings()
