"""
Hybrid Adaptive Algorithm Service

This service encapsulates the psychometric and machine learning logic of the system,
implementing Item Response Theory (IRT) for ability estimation and Bayesian Knowledge
Tracing (BKT) for skill mastery tracking.
"""

import os
import logging
import time
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import structlog
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
from prometheus_client import Counter, Histogram, Gauge
import uvicorn

from src.config import Settings
from src.database import DatabaseManager
from src.models import HealthResponse
from src.routers import sessions
from src.metrics import MetricsManager

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Global instances
settings = Settings()
metrics_manager = MetricsManager()
db_manager = DatabaseManager(settings.db_conn_string, metrics_manager)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Algorithm Service", version="1.0.0", port=settings.port)
    
    # Initialize database connection
    await db_manager.initialize()
    logger.info("Database connection initialized")
    
    # Initialize metrics
    metrics_manager.initialize()
    logger.info("Metrics initialized")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Algorithm Service")
    await db_manager.close()


# Create FastAPI application
app = FastAPI(
    title="Hybrid Adaptive Algorithm Service",
    description="Psychometric and machine learning computations for adaptive testing",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(sessions.router, prefix="/v1", tags=["sessions"])


@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    """Log all requests and responses"""
    start_time = time.time()
    
    # Log request
    logger.info(
        "Request received",
        method=request.method,
        url=str(request.url),
        client_ip=request.client.host if request.client else None
    )
    
    response = await call_next(request)
    
    # Log response
    process_time = time.time() - start_time
    logger.info(
        "Request completed",
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        process_time=process_time
    )
    
    # Record metrics
    metrics_manager.record_request(
        method=request.method,
        endpoint=str(request.url.path),
        status_code=str(response.status_code),
        duration=process_time
    )
    
    return response


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(
        "Unhandled exception",
        method=request.method,
        url=str(request.url),
        error=str(exc),
        exc_info=True
    )
    
    metrics_manager.record_error("unhandled_exception")
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }
    )


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Check database connectivity
        db_healthy = await db_manager.health_check()
        
        status = "healthy" if db_healthy else "unhealthy"
        
        return HealthResponse(
            status=status,
            service="algorithm",
            version="1.0.0",
            database_connected=db_healthy
        )
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return generate_latest()


if __name__ == "__main__":
    import time
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.port,
        log_level=settings.log_level.lower(),
        reload=settings.debug
    )
