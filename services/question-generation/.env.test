# Test Environment Configuration for Question Generation Service

# Service Configuration
SERVICE_NAME=question-generation-service
SERVICE_VERSION=1.0.0
PORT=8080
LOG_LEVEL=info

# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5433
DATABASE_NAME=test_ai_test_services
DATABASE_USER=postgres
DATABASE_PASSWORD=password
DATABASE_SSL_MODE=disable

# Connection Pool Settings
MAX_OPEN_CONNS=10
MAX_IDLE_CONNS=5
CONN_MAX_LIFETIME=1h

# Test-specific settings
SKIP_INTEGRATION_TESTS=false
TEST_TIMEOUT=30s
INTEGRATION_TEST_TIMEOUT=2m

# Test Database Configuration (for CI/CD)
TEST_DB_HOST=localhost
TEST_DB_PORT=5433
TEST_DB_NAME=test_ai_test_services
TEST_DB_USER=postgres
TEST_DB_PASSWORD=password
