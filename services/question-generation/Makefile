# Question Generation Service Makefile

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=question-generation-service
BINARY_UNIX=$(BINARY_NAME)_unix

# Test parameters
TEST_TIMEOUT=30s
INTEGRATION_TEST_TIMEOUT=2m

# Build the application
build:
	$(GOBUILD) -o $(BINARY_NAME) -v .

# Build for Linux
build-linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(BINARY_UNIX) -v .

# Clean build artifacts
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_UNIX)

# Run unit tests
test:
	$(GOTEST) -v -timeout $(TEST_TIMEOUT) ./internal/...

# Run unit tests with coverage
test-coverage:
	$(GOTEST) -v -timeout $(TEST_TIMEOUT) -coverprofile=coverage.out ./internal/...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Run integration tests (requires database)
test-integration:
	$(GOTEST) -v -timeout $(INTEGRATION_TEST_TIMEOUT) -tags=integration .

# Run all tests
test-all: test test-integration

# Run tests with race detection
test-race:
	$(GOTEST) -v -timeout $(TEST_TIMEOUT) -race ./internal/...

# Run benchmarks
benchmark:
	$(GOTEST) -v -timeout $(TEST_TIMEOUT) -bench=. -benchmem ./internal/...

# Lint the code (requires golangci-lint)
lint:
	golangci-lint run

# Format the code
fmt:
	$(GOCMD) fmt ./...

# Vet the code
vet:
	$(GOCMD) vet ./...

# Download dependencies
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# Update dependencies
deps-update:
	$(GOGET) -u ./...
	$(GOMOD) tidy

# Run the application
run:
	$(GOBUILD) -o $(BINARY_NAME) -v .
	./$(BINARY_NAME)

# Run the application with development settings
run-dev:
	LOG_LEVEL=debug PORT=8080 ./$(BINARY_NAME)

# Docker build
docker-build:
	docker build -t $(BINARY_NAME):latest .

# Docker run
docker-run:
	docker run -p 8080:8080 $(BINARY_NAME):latest

# Setup test database (requires Docker)
setup-test-db:
	docker run --name test-postgres \
		-e POSTGRES_DB=test_ai_test_services \
		-e POSTGRES_USER=postgres \
		-e POSTGRES_PASSWORD=password \
		-p 5433:5432 \
		-d postgres:15
	@echo "Test database started on port 5433"
	@echo "Run 'make teardown-test-db' to stop and remove the container"

# Teardown test database
teardown-test-db:
	docker stop test-postgres || true
	docker rm test-postgres || true

# Run tests with test database
test-with-db: setup-test-db
	@echo "Waiting for database to be ready..."
	@sleep 5
	TEST_DB_HOST=localhost \
	TEST_DB_PORT=5433 \
	TEST_DB_NAME=test_ai_test_services \
	TEST_DB_USER=postgres \
	TEST_DB_PASSWORD=password \
	$(GOTEST) -v -timeout $(INTEGRATION_TEST_TIMEOUT) .
	$(MAKE) teardown-test-db

# Generate mocks (requires mockery)
generate-mocks:
	mockery --dir=internal/repository --name=QuestionRepository --output=internal/mocks
	mockery --dir=internal/repository --name=ContentRepository --output=internal/mocks

# Security scan (requires gosec)
security-scan:
	gosec ./...

# Check for vulnerabilities (requires govulncheck)
vuln-check:
	govulncheck ./...

# Pre-commit checks
pre-commit: fmt vet lint test

# CI pipeline
ci: deps fmt vet lint test-coverage security-scan

# Development setup
dev-setup:
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GOGET) github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	$(GOGET) golang.org/x/vuln/cmd/govulncheck@latest
	$(GOGET) github.com/vektra/mockery/v2@latest

# Help
help:
	@echo "Available targets:"
	@echo "  build              - Build the application"
	@echo "  build-linux        - Build for Linux"
	@echo "  clean              - Clean build artifacts"
	@echo "  test               - Run unit tests"
	@echo "  test-coverage      - Run unit tests with coverage"
	@echo "  test-integration   - Run integration tests"
	@echo "  test-all           - Run all tests"
	@echo "  test-race          - Run tests with race detection"
	@echo "  test-with-db       - Run tests with temporary database"
	@echo "  benchmark          - Run benchmarks"
	@echo "  lint               - Lint the code"
	@echo "  fmt                - Format the code"
	@echo "  vet                - Vet the code"
	@echo "  deps               - Download dependencies"
	@echo "  deps-update        - Update dependencies"
	@echo "  run                - Run the application"
	@echo "  run-dev            - Run with development settings"
	@echo "  docker-build       - Build Docker image"
	@echo "  docker-run         - Run Docker container"
	@echo "  setup-test-db      - Setup test database"
	@echo "  teardown-test-db   - Teardown test database"
	@echo "  generate-mocks     - Generate mocks"
	@echo "  security-scan      - Run security scan"
	@echo "  vuln-check         - Check for vulnerabilities"
	@echo "  pre-commit         - Run pre-commit checks"
	@echo "  ci                 - Run CI pipeline"
	@echo "  dev-setup          - Setup development tools"
	@echo "  help               - Show this help"

.PHONY: build build-linux clean test test-coverage test-integration test-all test-race test-with-db benchmark lint fmt vet deps deps-update run run-dev docker-build docker-run setup-test-db teardown-test-db generate-mocks security-scan vuln-check pre-commit ci dev-setup help
