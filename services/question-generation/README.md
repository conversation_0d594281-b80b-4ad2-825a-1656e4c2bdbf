# Question Generation Service

The Question Generation Service manages the item bank for the Hybrid Adaptive Test Generation Service. It provides APIs for retrieving questions based on specified criteria and supports content management operations for administrators.

## Features

- **Question Retrieval**: Find questions based on skill, difficulty, and other criteria
- **Content Management**: CRUD operations for questions, skills, and subjects
- **Skill Mapping**: Associate questions with skills they assess
- **Version Control**: Support for question versioning and status management
- **Performance Optimization**: Efficient database queries with proper indexing
- **Metrics & Monitoring**: Prometheus metrics for usage tracking
- **Audit Logging**: Track question serving and content changes

## Technology Stack

- **Go 1.21+**
- **Gorilla Mux** - HTTP routing
- **PostgreSQL** - Database with content and models schemas
- **Prometheus Client** - Metrics collection
- **Structured Logging** - JSON-formatted logs

## API Endpoints

### Query Questions (Primary Endpoint)
**POST** `/v1/questions/query`

Retrieves a question matching the specified criteria. This is the primary endpoint used by the Orchestrator service to get the "next question" during adaptive testing.

**Request Body:**
```json
{
  "skill_ids": ["550e8400-e29b-41d4-a716-************"],
  "target_difficulty": 0.7,
  "difficulty_range": {
    "min": 0.5,
    "max": 0.9
  },
  "exclude_question_ids": ["550e8400-e29b-41d4-a716-************"],
  "question_type": "multiple_choice",
  "strategy": "adaptive"
}
```

**Request Parameters:**
- `skill_ids` (optional): Array of skill UUIDs to filter questions
- `target_difficulty` (optional): Target difficulty value (-10.0 to 10.0)
- `difficulty_range` (optional): Range of acceptable difficulty values
- `exclude_question_ids` (optional): Array of question version IDs to exclude
- `question_type` (optional): Type of question to retrieve
- `strategy` (optional): Selection strategy

**Response (200 OK):**
```json
{
  "question_version_id": "550e8400-e29b-41d4-a716-************",
  "question_text": "What is the result of 2 + 2?",
  "options": ["3", "4", "5", "6"],
  "skill_ids": ["550e8400-e29b-41d4-a716-************"],
  "difficulty": 0.75,
  "asset_urls": ["https://example.com/image.png"],
  "explanation": "Addition is a basic arithmetic operation.",
  "metadata": {
    "type": "multiple_choice",
    "correct_answer": "4"
  }
}
```

**Error Responses:**
- `400 Bad Request`: Invalid request parameters
- `404 Not Found`: No questions match the criteria
- `500 Internal Server Error`: Database or server error

### Get Question Details
```
GET /v1/questions/{questionId}
```

### Create Question
```
POST /v1/questions
Content-Type: application/json

{
  "subject_id": "uuid",
  "title": "Question Title",
  "text": "Question content...",
  "skill_ids": ["skill_uuid"],
  "metadata": {
    "type": "multiple_choice",
    "options": ["A", "B", "C", "D"],
    "correct_answer": "B"
  }
}
```

### Add Question Version
```
PUT /v1/questions/{questionId}/versions
Content-Type: application/json

{
  "text": "Updated question content...",
  "metadata": {...}
}
```

### List Skills
```
GET /v1/skills?subject_id=uuid
```

### Create Skill
```
POST /v1/skills
Content-Type: application/json

{
  "name": "Skill Name",
  "skill_domain_id": "uuid",
  "description": "Skill description"
}
```

## Configuration

Environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | `8082` | HTTP server port |
| `DB_CONN_STRING` | `postgres://localhost/orchestrator?sslmode=disable` | PostgreSQL connection string |
| `LOG_LEVEL` | `info` | Log level |

## Database Schema

The service primarily interacts with:
- `content.questions` - Question metadata
- `content.question_versions` - Question content and versions
- `content.question_skill_mapping` - Question-skill associations
- `content.skills` - Skill definitions
- `models.item_response_theory_parameters` - Question difficulty parameters

## Building and Running

### Build
```bash
go mod tidy
go build .
```

### Run
```bash
./question-generation-service
```

### Development
```bash
go run main.go
```

## Question Selection Algorithm

The service implements intelligent question selection:

1. **Skill Filtering**: Match questions to required skills
2. **Difficulty Matching**: Find questions within target difficulty range
3. **Status Filtering**: Only consider active questions
4. **Exposure Control**: Exclude previously used questions
5. **Randomization**: Select randomly from matching questions

## Testing

```bash
go test ./...
```

## Monitoring

### Metrics
- Questions served counter (by skill/difficulty)
- Query execution time histograms
- Content creation counters
- Cache hit rates (if caching implemented)

### Logging
- Question retrieval events
- Content management operations
- Query performance metrics
- Error conditions and debugging info

## Deployment

The service is designed for containerized deployment:

```bash
docker build -t question-service .
docker run -p 8082:8082 question-service
```
