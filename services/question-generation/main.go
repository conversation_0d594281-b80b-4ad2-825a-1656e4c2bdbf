package main

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gorilla/mux"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/sirupsen/logrus"

	"question-generation-service/internal/config"
	"question-generation-service/internal/database"
	"question-generation-service/internal/handlers"
	"question-generation-service/internal/metrics"
	"question-generation-service/internal/middleware"
	"question-generation-service/internal/repository"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Setup logger
	logger := setupLogger(cfg.LogLevel)
	logger.WithFields(logrus.Fields{
		"service": cfg.ServiceName,
		"version": cfg.ServiceVersion,
		"port":    cfg.Port,
	}).Info("Starting Question Generation Service")

	// Initialize metrics
	metricsCollector := metrics.NewMetrics()

	// Connect to database
	db, err := database.New(cfg, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to database")
	}
	defer db.Close()

	// Initialize repositories
	questionRepo := repository.NewQuestionRepository(db, logger)
	contentRepo := repository.NewContentRepository(db, logger)

	// Initialize handlers
	questionHandler := handlers.NewQuestionHandler(questionRepo, contentRepo, logger)
	skillHandler := handlers.NewSkillHandler(contentRepo, logger)

	// Setup HTTP server
	server := setupServer(cfg, logger, metricsCollector, questionHandler, skillHandler)

	// Start server in a goroutine
	go func() {
		logger.WithField("port", cfg.Port).Info("HTTP server starting")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("HTTP server failed to start")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Attempt graceful shutdown
	if err := server.Shutdown(ctx); err != nil {
		logger.WithError(err).Error("Server forced to shutdown")
	} else {
		logger.Info("Server shutdown completed")
	}
}

// setupLogger configures the logger based on the log level
func setupLogger(logLevel string) *logrus.Logger {
	logger := logrus.New()
	
	// Set log format to JSON for structured logging
	logger.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: time.RFC3339,
	})

	// Set log level
	level, err := logrus.ParseLevel(logLevel)
	if err != nil {
		logger.WithError(err).Warn("Invalid log level, defaulting to info")
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	return logger
}

// setupServer configures and returns the HTTP server
func setupServer(cfg *config.Config, logger *logrus.Logger, metrics *metrics.Metrics, questionHandler *handlers.QuestionHandler, skillHandler *handlers.SkillHandler) *http.Server {
	router := mux.NewRouter()

	// Add middleware
	router.Use(middleware.RecoveryMiddleware(logger))
	router.Use(middleware.LoggingMiddleware(logger))
	router.Use(middleware.MetricsMiddleware(metrics))
	router.Use(middleware.CORSMiddleware())

	// Health check endpoint
	router.HandleFunc("/health", middleware.HealthCheckMiddleware()).Methods("GET")

	// Metrics endpoint
	router.Handle("/metrics", promhttp.Handler()).Methods("GET")

	// API routes
	apiRouter := router.PathPrefix("/v1").Subrouter()

	// Question routes
	apiRouter.HandleFunc("/questions/query", questionHandler.QueryQuestions).Methods("POST")
	apiRouter.HandleFunc("/questions/{questionId}", questionHandler.GetQuestion).Methods("GET")
	apiRouter.HandleFunc("/questions", questionHandler.CreateQuestion).Methods("POST")
	apiRouter.HandleFunc("/questions/{questionId}/versions", questionHandler.CreateQuestionVersion).Methods("PUT")

	// Skill routes
	apiRouter.HandleFunc("/skills", skillHandler.GetSkills).Methods("GET")
	apiRouter.HandleFunc("/skills", skillHandler.CreateSkill).Methods("POST")

	// Create server
	server := &http.Server{
		Addr:         ":" + cfg.Port,
		Handler:      router,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	return server
}
