package main

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"question-generation-service/internal/config"
	"question-generation-service/internal/database"
	"question-generation-service/internal/handlers"
	"question-generation-service/internal/metrics"
	"question-generation-service/internal/middleware"
	"question-generation-service/internal/models"
	"question-generation-service/internal/repository"
)

// IntegrationTestSuite contains integration tests for the Question Generation Service
type IntegrationTestSuite struct {
	suite.Suite
	server   *httptest.Server
	db       *database.DB
	logger   *logrus.Logger
	testData *TestData
}

// TestData holds test data for integration tests
type TestData struct {
	SubjectID     uuid.UUID
	DomainID      uuid.UUID
	SkillID1      uuid.UUID
	SkillID2      uuid.UUID
	QuestionID    uuid.UUID
	QuestionVerID uuid.UUID
}

// SetupSuite runs once before all tests in the suite
func (suite *IntegrationTestSuite) SetupSuite() {
	// Skip integration tests if no database is available
	if os.Getenv("SKIP_INTEGRATION_TESTS") == "true" {
		suite.T().Skip("Skipping integration tests")
	}

	// Setup logger
	suite.logger = logrus.New()
	suite.logger.SetLevel(logrus.FatalLevel) // Suppress logs during tests

	// Setup test database connection
	DatabaseHost := getEnvOrDefault("TEST_DB_HOST", "localhost")
	DatabasePort := getEnvOrDefault("TEST_DB_PORT", "5432")
	DatabaseName := getEnvOrDefault("TEST_DB_NAME", "test_ai_test_services")
	DatabaseUser := getEnvOrDefault("TEST_DB_USER", "postgres")
	DatabasePassword := getEnvOrDefault("TEST_DB_PASSWORD", "password")
	DatabaseSSLMode := "disable"

	cfg := &config.Config{
		DatabaseURL:     "postgres://" + DatabaseUser + ":" + DatabasePassword + "@" + DatabaseHost + ":" + DatabasePort + "/" + DatabaseName + "?sslmode=" + DatabaseSSLMode,
		MaxOpenConns:    10,
		MaxIdleConns:    5,
		ConnMaxLifetime: time.Hour,
	}

	var err error
	suite.db, err = database.New(cfg, suite.logger)
	if err != nil {
		suite.T().Skipf("Cannot connect to test database: %v", err)
	}

	// Setup test data
	suite.setupTestData()

	// Setup HTTP server
	suite.setupServer()
}

// TearDownSuite runs once after all tests in the suite
func (suite *IntegrationTestSuite) TearDownSuite() {
	if suite.server != nil {
		suite.server.Close()
	}
	if suite.db != nil {
		suite.cleanupTestData()
		suite.db.Close()
	}
}

// SetupTest runs before each test
func (suite *IntegrationTestSuite) SetupTest() {
	// Clean up any test data from previous tests
	suite.cleanupQuestionData()
}

// setupTestData creates test data in the database
func (suite *IntegrationTestSuite) setupTestData() {
	suite.testData = &TestData{
		SubjectID:     uuid.New(),
		DomainID:      uuid.New(),
		SkillID1:      uuid.New(),
		SkillID2:      uuid.New(),
		QuestionID:    uuid.New(),
		QuestionVerID: uuid.New(),
	}

	ctx := context.Background()

	// Create test subject
	_, err := suite.db.ExecContext(ctx, `
		INSERT INTO content.subjects (id, name, description, created_at)
		VALUES ($1, 'Test Subject', 'Test subject for integration tests', NOW())
		ON CONFLICT (id) DO NOTHING
	`, suite.testData.SubjectID)
	require.NoError(suite.T(), err)

	// Create test skill domain
	_, err = suite.db.ExecContext(ctx, `
		INSERT INTO content.skill_domains (id, subject_id, name, description, created_at)
		VALUES ($1, $2, 'Test Domain', 'Test domain for integration tests', NOW())
		ON CONFLICT (id) DO NOTHING
	`, suite.testData.DomainID, suite.testData.SubjectID)
	require.NoError(suite.T(), err)

	// Create test skills
	_, err = suite.db.ExecContext(ctx, `
		INSERT INTO content.skills (id, skill_domain_id, name, description, created_at)
		VALUES ($1, $2, 'Addition', 'Basic addition skills', NOW()),
		       ($3, $2, 'Subtraction', 'Basic subtraction skills', NOW())
		ON CONFLICT (id) DO NOTHING
	`, suite.testData.SkillID1, suite.testData.DomainID, suite.testData.SkillID2)
	require.NoError(suite.T(), err)
}

// cleanupTestData removes test data from the database
func (suite *IntegrationTestSuite) cleanupTestData() {
	ctx := context.Background()

	// Clean up in reverse order of creation
	suite.db.ExecContext(ctx, "DELETE FROM content.skills WHERE skill_domain_id = $1", suite.testData.DomainID)
	suite.db.ExecContext(ctx, "DELETE FROM content.skill_domains WHERE id = $1", suite.testData.DomainID)
	suite.db.ExecContext(ctx, "DELETE FROM content.subjects WHERE id = $1", suite.testData.SubjectID)
}

// cleanupQuestionData removes question data created during tests
func (suite *IntegrationTestSuite) cleanupQuestionData() {
	ctx := context.Background()

	// Clean up question-related data
	suite.db.ExecContext(ctx, "DELETE FROM models.item_response_theory_parameters WHERE question_version_id IN (SELECT id FROM content.question_versions WHERE question_id IN (SELECT id FROM content.questions WHERE subject_id = $1))", suite.testData.SubjectID)
	suite.db.ExecContext(ctx, "DELETE FROM content.question_assets WHERE question_version_id IN (SELECT id FROM content.question_versions WHERE question_id IN (SELECT id FROM content.questions WHERE subject_id = $1))", suite.testData.SubjectID)
	suite.db.ExecContext(ctx, "DELETE FROM content.question_skill_mapping WHERE question_version_id IN (SELECT id FROM content.question_versions WHERE question_id IN (SELECT id FROM content.questions WHERE subject_id = $1))", suite.testData.SubjectID)
	suite.db.ExecContext(ctx, "DELETE FROM content.question_versions WHERE question_id IN (SELECT id FROM content.questions WHERE subject_id = $1)", suite.testData.SubjectID)
	suite.db.ExecContext(ctx, "DELETE FROM content.questions WHERE subject_id = $1", suite.testData.SubjectID)
}

// setupServer creates the HTTP server for testing
func (suite *IntegrationTestSuite) setupServer() {
	// Initialize repositories
	questionRepo := repository.NewQuestionRepository(suite.db, suite.logger)
	contentRepo := repository.NewContentRepository(suite.db, suite.logger)

	// Initialize handlers
	questionHandler := handlers.NewQuestionHandler(questionRepo, contentRepo, suite.logger)
	skillHandler := handlers.NewSkillHandler(contentRepo, suite.logger)

	// Initialize metrics
	metricsCollector := metrics.NewMetrics()

	// Setup router
	router := mux.NewRouter()
	router.Use(middleware.LoggingMiddleware(suite.logger))
	router.Use(middleware.MetricsMiddleware(metricsCollector))

	// Health check
	router.HandleFunc("/health", middleware.HealthCheckMiddleware()).Methods("GET")

	// API routes
	apiRouter := router.PathPrefix("/v1").Subrouter()
	apiRouter.HandleFunc("/questions/query", questionHandler.QueryQuestions).Methods("POST")
	apiRouter.HandleFunc("/questions/{questionId}", questionHandler.GetQuestion).Methods("GET")
	apiRouter.HandleFunc("/questions", questionHandler.CreateQuestion).Methods("POST")
	apiRouter.HandleFunc("/skills", skillHandler.GetSkills).Methods("GET")
	apiRouter.HandleFunc("/skills", skillHandler.CreateSkill).Methods("POST")

	// Create test server
	suite.server = httptest.NewServer(router)
}

// TestHealthCheck tests the health check endpoint
func (suite *IntegrationTestSuite) TestHealthCheck() {
	resp, err := http.Get(suite.server.URL + "/health")
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	assert.Equal(suite.T(), "application/json", resp.Header.Get("Content-Type"))

	var healthResp map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&healthResp)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "healthy", healthResp["status"])
}

// TestCreateAndQueryQuestion tests the full flow of creating and querying a question
func (suite *IntegrationTestSuite) TestCreateAndQueryQuestion() {
	// Step 1: Create a question
	createReq := models.CreateQuestionRequest{
		SubjectID:  &suite.testData.SubjectID,
		Title:      func() *string { s := "Integration Test Question"; return &s }(),
		Text:       "What is 2 + 2?",
		SkillIDs:   []uuid.UUID{suite.testData.SkillID1, suite.testData.SkillID2},
		Difficulty: func() *float64 { f := 0.5; return &f }(),
		AssetURLs:  []string{"https://example.com/test-image.png"},
		Metadata: map[string]interface{}{
			"type":           "multiple_choice",
			"options":        []string{"3", "4", "5", "6"},
			"correct_answer": "4",
		},
	}

	createBody, err := json.Marshal(createReq)
	require.NoError(suite.T(), err)

	resp, err := http.Post(suite.server.URL+"/v1/questions", "application/json", bytes.NewBuffer(createBody))
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusCreated, resp.StatusCode)

	var createResp models.CreateQuestionResponse
	err = json.NewDecoder(resp.Body).Decode(&createResp)
	require.NoError(suite.T(), err)
	assert.NotEqual(suite.T(), uuid.Nil, createResp.QuestionID)
	assert.NotEqual(suite.T(), uuid.Nil, createResp.QuestionVersionID)

	// Step 2: Query for the question
	queryReq := models.QuestionQueryRequest{
		SkillIDs:         []uuid.UUID{suite.testData.SkillID1},
		TargetDifficulty: func() *float64 { f := 0.5; return &f }(),
	}

	queryBody, err := json.Marshal(queryReq)
	require.NoError(suite.T(), err)

	resp, err = http.Post(suite.server.URL+"/v1/questions/query", "application/json", bytes.NewBuffer(queryBody))
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

	var queryResp models.QuestionQueryResponse
	err = json.NewDecoder(resp.Body).Decode(&queryResp)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), createResp.QuestionVersionID, queryResp.QuestionVersionID)
	assert.Equal(suite.T(), "What is 2 + 2?", queryResp.QuestionText)
	assert.Equal(suite.T(), 0.5, queryResp.Difficulty)
	assert.Contains(suite.T(), queryResp.SkillIDs, suite.testData.SkillID1.String())
	assert.Contains(suite.T(), queryResp.AssetURLs, "https://example.com/test-image.png")

	// Step 3: Get question by ID
	resp, err = http.Get(suite.server.URL + "/v1/questions/" + createResp.QuestionVersionID.String())
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

	var getResp models.QuestionQueryResponse
	err = json.NewDecoder(resp.Body).Decode(&getResp)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), createResp.QuestionVersionID, getResp.QuestionVersionID)
	assert.Equal(suite.T(), "What is 2 + 2?", getResp.QuestionText)
}

// TestCreateAndGetSkills tests skill creation and retrieval
func (suite *IntegrationTestSuite) TestCreateAndGetSkills() {
	// Step 1: Get existing skills
	resp, err := http.Get(suite.server.URL + "/v1/skills?subject_id=" + suite.testData.SubjectID.String())
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

	var getResp models.SkillListResponse
	err = json.NewDecoder(resp.Body).Decode(&getResp)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 2, getResp.Total) // We created 2 skills in setup
	assert.Len(suite.T(), getResp.Skills, 2)

	// Verify skill names
	skillNames := make(map[string]bool)
	for _, skill := range getResp.Skills {
		skillNames[skill.Skill.Name] = true
	}
	assert.True(suite.T(), skillNames["Addition"])
	assert.True(suite.T(), skillNames["Subtraction"])
}

// getEnvOrDefault returns environment variable value or default
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// TestIntegrationSuite runs the integration test suite
func TestIntegrationSuite(t *testing.T) {
	suite.Run(t, new(IntegrationTestSuite))
}
