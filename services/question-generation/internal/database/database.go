package database

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	_ "github.com/lib/pq"
	"github.com/sirupsen/logrus"

	"question-generation-service/internal/config"
)

// DB wraps the database connection
type DB struct {
	*sql.DB
	logger *logrus.Logger
}

// New creates a new database connection
func New(cfg *config.Config, logger *logrus.Logger) (*DB, error) {
	db, err := sql.Open("postgres", cfg.DatabaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Configure connection pool
	db.SetMaxOpenConns(cfg.MaxOpenConns)
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	db.SetConnMaxLifetime(cfg.ConnMaxLifetime)

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	logger.WithFields(logrus.Fields{
		"max_open_conns":    cfg.MaxOpenConns,
		"max_idle_conns":    cfg.MaxIdleConns,
		"conn_max_lifetime": cfg.ConnMaxLifetime,
	}).Info("Database connection established successfully")

	return &DB{
		DB:     db,
		logger: logger,
	}, nil
}

// NewForTesting creates a database wrapper for testing purposes
func NewForTesting(db *sql.DB, logger *logrus.Logger) *DB {
	return &DB{
		DB:     db,
		logger: logger,
	}
}

// Close closes the database connection
func (db *DB) Close() error {
	db.logger.Info("Closing database connection")
	return db.DB.Close()
}

// HealthCheck performs a health check on the database
func (db *DB) HealthCheck() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	return nil
}

// BeginTx starts a new transaction with the given context and options
func (db *DB) BeginTx(ctx context.Context, opts *sql.TxOptions) (*sql.Tx, error) {
	return db.DB.BeginTx(ctx, opts)
}

// ExecContext executes a query without returning any rows with context
func (db *DB) ExecContext(ctx context.Context, query string, args ...any) (sql.Result, error) {
	start := time.Now()
	result, err := db.DB.ExecContext(ctx, query, args...)
	duration := time.Since(start)

	db.logger.WithFields(logrus.Fields{
		"query":    query,
		"duration": duration,
		"error":    err,
	}).Debug("Database exec completed")

	return result, err
}

// QueryContext executes a query that returns rows with context
func (db *DB) QueryContext(ctx context.Context, query string, args ...any) (*sql.Rows, error) {
	start := time.Now()
	rows, err := db.DB.QueryContext(ctx, query, args...)
	duration := time.Since(start)

	db.logger.WithFields(logrus.Fields{
		"query":    query,
		"duration": duration,
		"error":    err,
	}).Debug("Database query completed")

	return rows, err
}

// QueryRowContext executes a query that is expected to return at most one row with context
func (db *DB) QueryRowContext(ctx context.Context, query string, args ...any) *sql.Row {
	start := time.Now()
	row := db.DB.QueryRowContext(ctx, query, args...)
	duration := time.Since(start)

	db.logger.WithFields(logrus.Fields{
		"query":    query,
		"duration": duration,
	}).Debug("Database query row completed")

	return row
}

// PrepareContext creates a prepared statement for later queries or executions with context
func (db *DB) PrepareContext(ctx context.Context, query string) (*sql.Stmt, error) {
	return db.DB.PrepareContext(ctx, query)
}
