package validation

import (
	"fmt"
	"strings"

	"github.com/google/uuid"

	"question-generation-service/internal/errors"
	"question-generation-service/internal/models"
)

// ValidateQuestionQueryRequest validates a question query request
func ValidateQuestionQueryRequest(req *models.QuestionQueryRequest) error {
	if req == nil {
		return errors.NewValidationError("Request body is required")
	}

	// Validate skill IDs
	for i, skillID := range req.SkillIDs {
		if skillID == uuid.Nil {
			return errors.NewValidationError(fmt.Sprintf("Invalid skill ID at index %d", i))
		}
	}

	// Validate difficulty range
	if req.DifficultyRange != nil {
		if req.DifficultyRange.Min != nil {
			if *req.DifficultyRange.Min < -10.0 || *req.DifficultyRange.Min > 10.0 {
				return errors.NewValidationError("Difficulty range min must be between -10.0 and 10.0")
			}
		}
		if req.DifficultyRange.Max != nil {
			if *req.DifficultyRange.Max < -10.0 || *req.DifficultyRange.Max > 10.0 {
				return errors.NewValidationError("Difficulty range max must be between -10.0 and 10.0")
			}
		}
		if req.DifficultyRange.Min != nil && req.DifficultyRange.Max != nil {
			if *req.DifficultyRange.Min > *req.DifficultyRange.Max {
				return errors.NewValidationError("Difficulty range min cannot be greater than max")
			}
		}
	}

	// Validate target difficulty
	if req.TargetDifficulty != nil {
		if *req.TargetDifficulty < -10.0 || *req.TargetDifficulty > 10.0 {
			return errors.NewValidationError("Target difficulty must be between -10.0 and 10.0")
		}
	}

	// Validate exclude question IDs
	for i, questionID := range req.ExcludeQuestionIDs {
		if questionID == uuid.Nil {
			return errors.NewValidationError(fmt.Sprintf("Invalid exclude question ID at index %d", i))
		}
	}

	return nil
}

// ValidateCreateQuestionRequest validates a create question request
func ValidateCreateQuestionRequest(req *models.CreateQuestionRequest) error {
	if req == nil {
		return errors.NewValidationError("Request body is required")
	}

	// Validate required fields
	if strings.TrimSpace(req.Text) == "" {
		return errors.NewValidationError("Question text is required")
	}

	if len(req.SkillIDs) == 0 {
		return errors.NewValidationError("At least one skill ID is required")
	}

	// Validate skill IDs
	for i, skillID := range req.SkillIDs {
		if skillID == uuid.Nil {
			return errors.NewValidationError(fmt.Sprintf("Invalid skill ID at index %d", i))
		}
	}

	// Validate subject ID if provided
	if req.SubjectID != nil && *req.SubjectID == uuid.Nil {
		return errors.NewValidationError("Invalid subject ID")
	}

	// Validate difficulty if provided
	if req.Difficulty != nil {
		if *req.Difficulty < -10.0 || *req.Difficulty > 10.0 {
			return errors.NewValidationError("Difficulty must be between -10.0 and 10.0")
		}
	}

	// Validate title length if provided
	if req.Title != nil && len(*req.Title) > 255 {
		return errors.NewValidationError("Title cannot exceed 255 characters")
	}

	// Validate asset URLs if provided
	for i, assetURL := range req.AssetURLs {
		if strings.TrimSpace(assetURL) == "" {
			return errors.NewValidationError(fmt.Sprintf("Asset URL at index %d cannot be empty", i))
		}
		if len(assetURL) > 255 {
			return errors.NewValidationError(fmt.Sprintf("Asset URL at index %d cannot exceed 255 characters", i))
		}
	}

	return nil
}

// ValidateCreateQuestionVersionRequest validates a create question version request
func ValidateCreateQuestionVersionRequest(req *models.CreateQuestionVersionRequest) error {
	if req == nil {
		return errors.NewValidationError("Request body is required")
	}

	// Validate required fields
	if strings.TrimSpace(req.Text) == "" {
		return errors.NewValidationError("Question text is required")
	}

	// Validate difficulty if provided
	if req.Difficulty != nil {
		if *req.Difficulty < -10.0 || *req.Difficulty > 10.0 {
			return errors.NewValidationError("Difficulty must be between -10.0 and 10.0")
		}
	}

	// Validate asset URLs if provided
	for i, assetURL := range req.AssetURLs {
		if strings.TrimSpace(assetURL) == "" {
			return errors.NewValidationError(fmt.Sprintf("Asset URL at index %d cannot be empty", i))
		}
		if len(assetURL) > 255 {
			return errors.NewValidationError(fmt.Sprintf("Asset URL at index %d cannot exceed 255 characters", i))
		}
	}

	return nil
}

// ValidateCreateSkillRequest validates a create skill request
func ValidateCreateSkillRequest(req *models.CreateSkillRequest) error {
	if req == nil {
		return errors.NewValidationError("Request body is required")
	}

	// Validate required fields
	if strings.TrimSpace(req.Name) == "" {
		return errors.NewValidationError("Skill name is required")
	}

	if req.SkillDomainID == uuid.Nil {
		return errors.NewValidationError("Skill domain ID is required")
	}

	// Validate name length
	if len(req.Name) > 255 {
		return errors.NewValidationError("Skill name cannot exceed 255 characters")
	}

	// Validate description length if provided
	if req.Description != nil && len(*req.Description) > 1000 {
		return errors.NewValidationError("Skill description cannot exceed 1000 characters")
	}

	return nil
}

// ValidateUUID validates a UUID string
func ValidateUUID(uuidStr string, fieldName string) error {
	if strings.TrimSpace(uuidStr) == "" {
		return errors.NewValidationError(fmt.Sprintf("%s is required", fieldName))
	}

	if _, err := uuid.Parse(uuidStr); err != nil {
		return errors.NewValidationError(fmt.Sprintf("Invalid %s format", fieldName))
	}

	return nil
}

// ValidatePaginationParams validates pagination parameters
func ValidatePaginationParams(page, limit int) error {
	if page < 1 {
		return errors.NewValidationError("Page must be greater than 0")
	}

	if limit < 1 || limit > 100 {
		return errors.NewValidationError("Limit must be between 1 and 100")
	}

	return nil
}
