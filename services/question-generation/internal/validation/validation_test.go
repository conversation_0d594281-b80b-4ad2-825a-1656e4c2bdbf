package validation

import (
	"testing"

	"github.com/google/uuid"

	"question-generation-service/internal/models"
)

func TestValidateQuestionQueryRequest(t *testing.T) {
	tests := []struct {
		name    string
		req     *models.QuestionQueryRequest
		wantErr bool
	}{
		{
			name:    "nil request",
			req:     nil,
			wantErr: true,
		},
		{
			name:    "empty request",
			req:     &models.QuestionQueryRequest{},
			wantErr: false,
		},
		{
			name: "valid request with skill IDs",
			req: &models.QuestionQueryRequest{
				SkillIDs: []uuid.UUID{uuid.New(), uuid.New()},
			},
			wantErr: false,
		},
		{
			name: "invalid skill ID",
			req: &models.QuestionQueryRequest{
				SkillIDs: []uuid.UUID{uuid.Nil},
			},
			wantErr: true,
		},
		{
			name: "valid target difficulty",
			req: &models.QuestionQueryRequest{
				TargetDifficulty: func() *float64 { f := 0.5; return &f }(),
			},
			wantErr: false,
		},
		{
			name: "invalid target difficulty - too low",
			req: &models.QuestionQueryRequest{
				TargetDifficulty: func() *float64 { f := -11.0; return &f }(),
			},
			wantErr: true,
		},
		{
			name: "invalid target difficulty - too high",
			req: &models.QuestionQueryRequest{
				TargetDifficulty: func() *float64 { f := 11.0; return &f }(),
			},
			wantErr: true,
		},
		{
			name: "valid difficulty range",
			req: &models.QuestionQueryRequest{
				DifficultyRange: &models.DifficultyRange{
					Min: func() *float64 { f := -2.0; return &f }(),
					Max: func() *float64 { f := 2.0; return &f }(),
				},
			},
			wantErr: false,
		},
		{
			name: "invalid difficulty range - min > max",
			req: &models.QuestionQueryRequest{
				DifficultyRange: &models.DifficultyRange{
					Min: func() *float64 { f := 2.0; return &f }(),
					Max: func() *float64 { f := -2.0; return &f }(),
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateQuestionQueryRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateQuestionQueryRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestValidateCreateQuestionRequest(t *testing.T) {
	validSkillID := uuid.New()
	validSubjectID := uuid.New()

	tests := []struct {
		name    string
		req     *models.CreateQuestionRequest
		wantErr bool
	}{
		{
			name:    "nil request",
			req:     nil,
			wantErr: true,
		},
		{
			name: "missing text",
			req: &models.CreateQuestionRequest{
				SkillIDs: []uuid.UUID{validSkillID},
			},
			wantErr: true,
		},
		{
			name: "missing skill IDs",
			req: &models.CreateQuestionRequest{
				Text: "What is 2+2?",
			},
			wantErr: true,
		},
		{
			name: "valid request",
			req: &models.CreateQuestionRequest{
				Text:     "What is 2+2?",
				SkillIDs: []uuid.UUID{validSkillID},
			},
			wantErr: false,
		},
		{
			name: "valid request with optional fields",
			req: &models.CreateQuestionRequest{
				SubjectID:  &validSubjectID,
				Title:      func() *string { s := "Math Question"; return &s }(),
				Text:       "What is 2+2?",
				SkillIDs:   []uuid.UUID{validSkillID},
				Difficulty: func() *float64 { f := 0.5; return &f }(),
				AssetURLs:  []string{"https://example.com/image.png"},
			},
			wantErr: false,
		},
		{
			name: "invalid difficulty",
			req: &models.CreateQuestionRequest{
				Text:       "What is 2+2?",
				SkillIDs:   []uuid.UUID{validSkillID},
				Difficulty: func() *float64 { f := 15.0; return &f }(),
			},
			wantErr: true,
		},
		{
			name: "invalid skill ID",
			req: &models.CreateQuestionRequest{
				Text:     "What is 2+2?",
				SkillIDs: []uuid.UUID{uuid.Nil},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateCreateQuestionRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateCreateQuestionRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestValidateCreateSkillRequest(t *testing.T) {
	validDomainID := uuid.New()

	tests := []struct {
		name    string
		req     *models.CreateSkillRequest
		wantErr bool
	}{
		{
			name:    "nil request",
			req:     nil,
			wantErr: true,
		},
		{
			name: "missing name",
			req: &models.CreateSkillRequest{
				SkillDomainID: validDomainID,
			},
			wantErr: true,
		},
		{
			name: "missing domain ID",
			req: &models.CreateSkillRequest{
				Name: "Addition",
			},
			wantErr: true,
		},
		{
			name: "valid request",
			req: &models.CreateSkillRequest{
				SkillDomainID: validDomainID,
				Name:          "Addition",
			},
			wantErr: false,
		},
		{
			name: "valid request with description",
			req: &models.CreateSkillRequest{
				SkillDomainID: validDomainID,
				Name:          "Addition",
				Description:   func() *string { s := "Basic addition skills"; return &s }(),
			},
			wantErr: false,
		},
		{
			name: "invalid domain ID",
			req: &models.CreateSkillRequest{
				SkillDomainID: uuid.Nil,
				Name:          "Addition",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateCreateSkillRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateCreateSkillRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestValidateUUID(t *testing.T) {
	tests := []struct {
		name      string
		uuidStr   string
		fieldName string
		wantErr   bool
	}{
		{
			name:      "empty string",
			uuidStr:   "",
			fieldName: "id",
			wantErr:   true,
		},
		{
			name:      "invalid UUID",
			uuidStr:   "invalid-uuid",
			fieldName: "id",
			wantErr:   true,
		},
		{
			name:      "valid UUID",
			uuidStr:   uuid.New().String(),
			fieldName: "id",
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateUUID(tt.uuidStr, tt.fieldName)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateUUID() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
