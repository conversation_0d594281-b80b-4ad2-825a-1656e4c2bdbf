package metrics

import (
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// Metrics holds all Prometheus metrics for the service
type Metrics struct {
	// Question serving metrics
	QuestionsServedTotal    prometheus.Counter
	QuestionsServedBySkill  *prometheus.CounterVec
	QuestionsServedByDiff   *prometheus.CounterVec
	QuestionQueryDuration   prometheus.Histogram
	QuestionQueryErrors     prometheus.Counter

	// Content creation metrics
	QuestionsCreatedTotal   prometheus.Counter
	SkillsCreatedTotal      prometheus.Counter
	VersionsCreatedTotal    prometheus.Counter
	ContentCreationDuration prometheus.Histogram
	ContentCreationErrors   prometheus.Counter

	// Database metrics
	DatabaseQueryDuration   prometheus.Histogram
	DatabaseConnections     prometheus.Gauge
	DatabaseErrors          prometheus.Counter

	// HTTP metrics
	HTTPRequestsTotal       *prometheus.CounterVec
	HTTPRequestDuration     *prometheus.HistogramVec
	HTTPResponseSize        *prometheus.HistogramVec
}

// NewMetrics creates and registers all Prometheus metrics
func NewMetrics() *Metrics {
	return &Metrics{
		// Question serving metrics
		QuestionsServedTotal: promauto.NewCounter(prometheus.CounterOpts{
			Name: "questions_served_total",
			Help: "Total number of questions served",
		}),
		QuestionsServedBySkill: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "questions_served_by_skill_total",
			Help: "Total number of questions served by skill",
		}, []string{"skill_id", "skill_name"}),
		QuestionsServedByDiff: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "questions_served_by_difficulty_total",
			Help: "Total number of questions served by difficulty range",
		}, []string{"difficulty_range"}),
		QuestionQueryDuration: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "question_query_duration_seconds",
			Help:    "Duration of question query operations",
			Buckets: prometheus.DefBuckets,
		}),
		QuestionQueryErrors: promauto.NewCounter(prometheus.CounterOpts{
			Name: "question_query_errors_total",
			Help: "Total number of question query errors",
		}),

		// Content creation metrics
		QuestionsCreatedTotal: promauto.NewCounter(prometheus.CounterOpts{
			Name: "questions_created_total",
			Help: "Total number of questions created",
		}),
		SkillsCreatedTotal: promauto.NewCounter(prometheus.CounterOpts{
			Name: "skills_created_total",
			Help: "Total number of skills created",
		}),
		VersionsCreatedTotal: promauto.NewCounter(prometheus.CounterOpts{
			Name: "question_versions_created_total",
			Help: "Total number of question versions created",
		}),
		ContentCreationDuration: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "content_creation_duration_seconds",
			Help:    "Duration of content creation operations",
			Buckets: prometheus.DefBuckets,
		}),
		ContentCreationErrors: promauto.NewCounter(prometheus.CounterOpts{
			Name: "content_creation_errors_total",
			Help: "Total number of content creation errors",
		}),

		// Database metrics
		DatabaseQueryDuration: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "database_query_duration_seconds",
			Help:    "Duration of database queries",
			Buckets: prometheus.DefBuckets,
		}),
		DatabaseConnections: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "database_connections_active",
			Help: "Number of active database connections",
		}),
		DatabaseErrors: promauto.NewCounter(prometheus.CounterOpts{
			Name: "database_errors_total",
			Help: "Total number of database errors",
		}),

		// HTTP metrics
		HTTPRequestsTotal: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "http_requests_total",
			Help: "Total number of HTTP requests",
		}, []string{"method", "endpoint", "status_code"}),
		HTTPRequestDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "http_request_duration_seconds",
			Help:    "Duration of HTTP requests",
			Buckets: prometheus.DefBuckets,
		}, []string{"method", "endpoint"}),
		HTTPResponseSize: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "http_response_size_bytes",
			Help:    "Size of HTTP responses",
			Buckets: prometheus.ExponentialBuckets(100, 10, 8),
		}, []string{"method", "endpoint"}),
	}
}

// RecordQuestionServed records metrics for a served question
func (m *Metrics) RecordQuestionServed(skillID, skillName string, difficulty float64) {
	m.QuestionsServedTotal.Inc()
	
	if skillID != "" && skillName != "" {
		m.QuestionsServedBySkill.WithLabelValues(skillID, skillName).Inc()
	}
	
	// Categorize difficulty into ranges
	diffRange := m.getDifficultyRange(difficulty)
	m.QuestionsServedByDiff.WithLabelValues(diffRange).Inc()
}

// RecordQuestionQuery records metrics for a question query operation
func (m *Metrics) RecordQuestionQuery(duration time.Duration, success bool) {
	m.QuestionQueryDuration.Observe(duration.Seconds())
	if !success {
		m.QuestionQueryErrors.Inc()
	}
}

// RecordQuestionCreated records metrics for question creation
func (m *Metrics) RecordQuestionCreated(duration time.Duration, success bool) {
	m.ContentCreationDuration.Observe(duration.Seconds())
	if success {
		m.QuestionsCreatedTotal.Inc()
	} else {
		m.ContentCreationErrors.Inc()
	}
}

// RecordSkillCreated records metrics for skill creation
func (m *Metrics) RecordSkillCreated(duration time.Duration, success bool) {
	m.ContentCreationDuration.Observe(duration.Seconds())
	if success {
		m.SkillsCreatedTotal.Inc()
	} else {
		m.ContentCreationErrors.Inc()
	}
}

// RecordVersionCreated records metrics for version creation
func (m *Metrics) RecordVersionCreated(duration time.Duration, success bool) {
	m.ContentCreationDuration.Observe(duration.Seconds())
	if success {
		m.VersionsCreatedTotal.Inc()
	} else {
		m.ContentCreationErrors.Inc()
	}
}

// RecordDatabaseQuery records metrics for database operations
func (m *Metrics) RecordDatabaseQuery(duration time.Duration, success bool) {
	m.DatabaseQueryDuration.Observe(duration.Seconds())
	if !success {
		m.DatabaseErrors.Inc()
	}
}

// RecordHTTPRequest records metrics for HTTP requests
func (m *Metrics) RecordHTTPRequest(method, endpoint string, statusCode int, duration time.Duration, responseSize int) {
	statusCodeStr := strconv.Itoa(statusCode)
	m.HTTPRequestsTotal.WithLabelValues(method, endpoint, statusCodeStr).Inc()
	m.HTTPRequestDuration.WithLabelValues(method, endpoint).Observe(duration.Seconds())
	m.HTTPResponseSize.WithLabelValues(method, endpoint).Observe(float64(responseSize))
}

// UpdateDatabaseConnections updates the database connections gauge
func (m *Metrics) UpdateDatabaseConnections(count int) {
	m.DatabaseConnections.Set(float64(count))
}

// getDifficultyRange categorizes difficulty into ranges for metrics
func (m *Metrics) getDifficultyRange(difficulty float64) string {
	switch {
	case difficulty < -2.0:
		return "very_easy"
	case difficulty < -1.0:
		return "easy"
	case difficulty < 0.0:
		return "below_average"
	case difficulty < 1.0:
		return "above_average"
	case difficulty < 2.0:
		return "hard"
	default:
		return "very_hard"
	}
}
