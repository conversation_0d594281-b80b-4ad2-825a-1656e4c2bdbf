package metrics

import (
	"testing"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/testutil"
	"github.com/stretchr/testify/assert"
)

func TestMetrics_RecordQuestionServed(t *testing.T) {
	// Create a new registry for isolated testing
	registry := prometheus.NewRegistry()

	// Create metrics with custom registry
	metrics := &Metrics{
		QuestionsServedTotal: prometheus.NewCounter(prometheus.CounterOpts{
			Name: "questions_served_total",
			Help: "Total number of questions served",
		}),
		QuestionsServedBySkill: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "questions_served_by_skill_total",
			Help: "Total number of questions served by skill",
		}, []string{"skill_id", "skill_name"}),
		QuestionsServedByDiff: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "questions_served_by_difficulty_total",
			Help: "Total number of questions served by difficulty range",
		}, []string{"difficulty_range"}),
	}

	// Register metrics
	registry.MustRegister(metrics.QuestionsServedTotal)
	registry.MustRegister(metrics.QuestionsServedBySkill)
	registry.MustRegister(metrics.QuestionsServedByDiff)

	tests := []struct {
		name               string
		skillID            string
		skillName          string
		difficulty         float64
		expectedDiffRange  string
		expectedTotalCount float64
	}{
		{
			name:               "very easy question",
			skillID:            "skill-1",
			skillName:          "Addition",
			difficulty:         -3.0,
			expectedDiffRange:  "very_easy",
			expectedTotalCount: 1,
		},
		{
			name:               "easy question",
			skillID:            "skill-2",
			skillName:          "Subtraction",
			difficulty:         -1.5,
			expectedDiffRange:  "easy",
			expectedTotalCount: 2,
		},
		{
			name:               "below average question",
			skillID:            "skill-3",
			skillName:          "Multiplication",
			difficulty:         -0.5,
			expectedDiffRange:  "below_average",
			expectedTotalCount: 3,
		},
		{
			name:               "above average question",
			skillID:            "skill-4",
			skillName:          "Division",
			difficulty:         0.5,
			expectedDiffRange:  "above_average",
			expectedTotalCount: 4,
		},
		{
			name:               "hard question",
			skillID:            "skill-5",
			skillName:          "Algebra",
			difficulty:         1.5,
			expectedDiffRange:  "hard",
			expectedTotalCount: 5,
		},
		{
			name:               "very hard question",
			skillID:            "skill-6",
			skillName:          "Calculus",
			difficulty:         3.0,
			expectedDiffRange:  "very_hard",
			expectedTotalCount: 6,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Record the question served
			metrics.RecordQuestionServed(tt.skillID, tt.skillName, tt.difficulty)

			// Check total questions served counter
			totalCount := testutil.ToFloat64(metrics.QuestionsServedTotal)
			assert.Equal(t, tt.expectedTotalCount, totalCount)

			// Check skill-specific counter
			skillCounter := metrics.QuestionsServedBySkill.WithLabelValues(tt.skillID, tt.skillName)
			skillCount := testutil.ToFloat64(skillCounter)
			assert.Equal(t, float64(1), skillCount)

			// Check difficulty range counter
			diffCounter := metrics.QuestionsServedByDiff.WithLabelValues(tt.expectedDiffRange)
			diffCount := testutil.ToFloat64(diffCounter)
			assert.Equal(t, float64(1), diffCount)
		})
	}
}

func TestMetrics_RecordQuestionQuery(t *testing.T) {
	// Create metrics
	metrics := &Metrics{
		QuestionQueryDuration: prometheus.NewHistogram(prometheus.HistogramOpts{
			Name:    "question_query_duration_seconds",
			Help:    "Duration of question query operations",
			Buckets: prometheus.DefBuckets,
		}),
		QuestionQueryErrors: prometheus.NewCounter(prometheus.CounterOpts{
			Name: "question_query_errors_total",
			Help: "Total number of question query errors",
		}),
	}

	tests := []struct {
		name     string
		duration time.Duration
		success  bool
	}{
		{
			name:     "successful query",
			duration: 100 * time.Millisecond,
			success:  true,
		},
		{
			name:     "failed query",
			duration: 50 * time.Millisecond,
			success:  false,
		},
		{
			name:     "slow successful query",
			duration: 500 * time.Millisecond,
			success:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			initialErrorCount := testutil.ToFloat64(metrics.QuestionQueryErrors)

			// Record the query
			metrics.RecordQuestionQuery(tt.duration, tt.success)

			// Check duration histogram (should always be recorded)
			histogramCount := testutil.ToFloat64(metrics.QuestionQueryDuration)
			assert.Greater(t, histogramCount, float64(0))

			// Check error counter (should only increment on failure)
			expectedErrorCount := initialErrorCount
			if !tt.success {
				expectedErrorCount++
			}
			errorCount := testutil.ToFloat64(metrics.QuestionQueryErrors)
			assert.Equal(t, expectedErrorCount, errorCount)
		})
	}
}

func TestMetrics_RecordHTTPRequest(t *testing.T) {
	// Create metrics
	metrics := &Metrics{
		HTTPRequestsTotal: prometheus.NewCounterVec(prometheus.CounterOpts{
			Name: "http_requests_total",
			Help: "Total number of HTTP requests",
		}, []string{"method", "endpoint", "status_code"}),
		HTTPRequestDuration: prometheus.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "http_request_duration_seconds",
			Help:    "Duration of HTTP requests",
			Buckets: prometheus.DefBuckets,
		}, []string{"method", "endpoint"}),
		HTTPResponseSize: prometheus.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "http_response_size_bytes",
			Help:    "Size of HTTP responses",
			Buckets: prometheus.ExponentialBuckets(100, 10, 8),
		}, []string{"method", "endpoint"}),
	}

	tests := []struct {
		name         string
		method       string
		endpoint     string
		statusCode   int
		duration     time.Duration
		responseSize int
	}{
		{
			name:         "successful GET request",
			method:       "GET",
			endpoint:     "/v1/questions/{id}",
			statusCode:   200,
			duration:     50 * time.Millisecond,
			responseSize: 1024,
		},
		{
			name:         "successful POST request",
			method:       "POST",
			endpoint:     "/v1/questions/query",
			statusCode:   200,
			duration:     100 * time.Millisecond,
			responseSize: 2048,
		},
		{
			name:         "not found request",
			method:       "GET",
			endpoint:     "/v1/questions/{id}",
			statusCode:   404,
			duration:     25 * time.Millisecond,
			responseSize: 256,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Record the HTTP request
			metrics.RecordHTTPRequest(tt.method, tt.endpoint, tt.statusCode, tt.duration, tt.responseSize)

			// Check request counter (verify it doesn't panic)
			_ = metrics.HTTPRequestsTotal.WithLabelValues(tt.method, tt.endpoint, string(rune(tt.statusCode)))

			// Check duration histogram (verify it doesn't panic)
			_ = metrics.HTTPRequestDuration.WithLabelValues(tt.method, tt.endpoint)

			// Check response size histogram (verify it doesn't panic)
			_ = metrics.HTTPResponseSize.WithLabelValues(tt.method, tt.endpoint)

			// If we reach here without panicking, the metrics recording worked
			assert.True(t, true)
		})
	}
}

func TestMetrics_GetDifficultyRange(t *testing.T) {
	metrics := &Metrics{}

	tests := []struct {
		name       string
		difficulty float64
		expected   string
	}{
		{
			name:       "very easy",
			difficulty: -3.0,
			expected:   "very_easy",
		},
		{
			name:       "easy",
			difficulty: -1.5,
			expected:   "easy",
		},
		{
			name:       "below average",
			difficulty: -0.5,
			expected:   "below_average",
		},
		{
			name:       "above average",
			difficulty: 0.5,
			expected:   "above_average",
		},
		{
			name:       "hard",
			difficulty: 1.5,
			expected:   "hard",
		},
		{
			name:       "very hard",
			difficulty: 3.0,
			expected:   "very_hard",
		},
		{
			name:       "boundary case - exactly -2.0",
			difficulty: -2.0,
			expected:   "easy",
		},
		{
			name:       "boundary case - exactly 0.0",
			difficulty: 0.0,
			expected:   "above_average",
		},
		{
			name:       "boundary case - exactly 2.0",
			difficulty: 2.0,
			expected:   "very_hard",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := metrics.getDifficultyRange(tt.difficulty)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestMetrics_UpdateDatabaseConnections(t *testing.T) {
	// Create metrics
	metrics := &Metrics{
		DatabaseConnections: prometheus.NewGauge(prometheus.GaugeOpts{
			Name: "database_connections_active",
			Help: "Number of active database connections",
		}),
	}

	tests := []struct {
		name  string
		count int
	}{
		{
			name:  "zero connections",
			count: 0,
		},
		{
			name:  "five connections",
			count: 5,
		},
		{
			name:  "max connections",
			count: 100,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Update database connections
			metrics.UpdateDatabaseConnections(tt.count)

			// Check gauge value
			gaugeValue := testutil.ToFloat64(metrics.DatabaseConnections)
			assert.Equal(t, float64(tt.count), gaugeValue)
		})
	}
}

func TestNewMetrics(t *testing.T) {
	// Create new metrics instance
	metrics := NewMetrics()

	// Verify all metrics are initialized
	assert.NotNil(t, metrics.QuestionsServedTotal)
	assert.NotNil(t, metrics.QuestionsServedBySkill)
	assert.NotNil(t, metrics.QuestionsServedByDiff)
	assert.NotNil(t, metrics.QuestionQueryDuration)
	assert.NotNil(t, metrics.QuestionQueryErrors)
	assert.NotNil(t, metrics.QuestionsCreatedTotal)
	assert.NotNil(t, metrics.SkillsCreatedTotal)
	assert.NotNil(t, metrics.VersionsCreatedTotal)
	assert.NotNil(t, metrics.ContentCreationDuration)
	assert.NotNil(t, metrics.ContentCreationErrors)
	assert.NotNil(t, metrics.DatabaseQueryDuration)
	assert.NotNil(t, metrics.DatabaseConnections)
	assert.NotNil(t, metrics.DatabaseErrors)
	assert.NotNil(t, metrics.HTTPRequestsTotal)
	assert.NotNil(t, metrics.HTTPRequestDuration)
	assert.NotNil(t, metrics.HTTPResponseSize)

	// Test that metrics can be used without panicking
	metrics.RecordQuestionServed("test-skill", "Test Skill", 0.5)
	metrics.RecordQuestionQuery(100*time.Millisecond, true)
	metrics.UpdateDatabaseConnections(5)

	// If we reach here without panicking, the metrics are properly initialized
	assert.True(t, true)
}
