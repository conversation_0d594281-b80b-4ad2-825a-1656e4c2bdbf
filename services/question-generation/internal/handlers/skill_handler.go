package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"question-generation-service/internal/models"
	"question-generation-service/internal/repository"
)

// <PERSON>llHandler handles HTTP requests for skill operations
type SkillHandler struct {
	contentRepo *repository.ContentRepository
	logger      *logrus.Logger
}

// NewSkillHandler creates a new skill handler
func NewSkillHandler(contentRepo *repository.ContentRepository, logger *logrus.Logger) *SkillHandler {
	return &SkillHandler{
		contentRepo: contentRepo,
		logger:      logger,
	}
}

// GetSkills handles GET /v1/skills
func (h *SkillHandler) GetSkills(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
	defer cancel()

	// Parse query parameters
	var subjectID, domainID *uuid.UUID
	
	if subjectIDStr := r.URL.Query().Get("subject_id"); subjectIDStr != "" {
		if id, err := uuid.Parse(subjectIDStr); err == nil {
			subjectID = &id
		} else {
			h.logger.WithError(err).WithField("subject_id", subjectIDStr).Error("Invalid subject ID format")
			h.writeErrorResponse(w, "Invalid subject ID format", http.StatusBadRequest)
			return
		}
	}

	if domainIDStr := r.URL.Query().Get("domain_id"); domainIDStr != "" {
		if id, err := uuid.Parse(domainIDStr); err == nil {
			domainID = &id
		} else {
			h.logger.WithError(err).WithField("domain_id", domainIDStr).Error("Invalid domain ID format")
			h.writeErrorResponse(w, "Invalid domain ID format", http.StatusBadRequest)
			return
		}
	}

	// Get skills
	response, err := h.contentRepo.GetSkills(ctx, subjectID, domainID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get skills")
		h.writeErrorResponse(w, "Failed to retrieve skills", http.StatusInternalServerError)
		return
	}

	h.logger.WithFields(logrus.Fields{
		"skill_count": response.Total,
		"subject_id":  subjectID,
		"domain_id":   domainID,
	}).Info("Skills retrieved successfully")

	// Write response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.WithError(err).Error("Failed to encode response")
	}
}

// CreateSkill handles POST /v1/skills
func (h *SkillHandler) CreateSkill(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
	defer cancel()

	// Parse request body
	var req models.CreateSkillRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.logger.WithError(err).Error("Failed to decode request body")
		h.writeErrorResponse(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if err := h.validateCreateSkillRequest(&req); err != nil {
		h.logger.WithError(err).Error("Request validation failed")
		h.writeErrorResponse(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Create skill
	response, err := h.contentRepo.CreateSkill(ctx, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create skill")
		h.writeErrorResponse(w, "Failed to create skill", http.StatusInternalServerError)
		return
	}

	h.logger.WithFields(logrus.Fields{
		"skill_id":        response.SkillID,
		"skill_domain_id": req.SkillDomainID,
		"name":            req.Name,
	}).Info("Skill created successfully")

	// Write response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.WithError(err).Error("Failed to encode response")
	}
}

// validateCreateSkillRequest validates the create skill request
func (h *SkillHandler) validateCreateSkillRequest(req *models.CreateSkillRequest) error {
	if req.Name == "" {
		return fmt.Errorf("skill name is required")
	}
	if req.SkillDomainID == uuid.Nil {
		return fmt.Errorf("skill domain ID is required")
	}
	return nil
}

// writeErrorResponse writes an error response
func (h *SkillHandler) writeErrorResponse(w http.ResponseWriter, message string, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	
	errorResponse := models.ErrorResponse{
		Error:   http.StatusText(statusCode),
		Message: message,
		Code:    statusCode,
	}
	
	if err := json.NewEncoder(w).Encode(errorResponse); err != nil {
		h.logger.WithError(err).Error("Failed to encode error response")
	}
}
