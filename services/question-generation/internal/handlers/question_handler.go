package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"

	"question-generation-service/internal/models"
	"question-generation-service/internal/repository"
)

// Question<PERSON><PERSON><PERSON> handles HTTP requests for question operations
type Question<PERSON><PERSON><PERSON> struct {
	questionRepo repository.QuestionRepositoryInterface
	contentRepo  repository.ContentRepositoryInterface
	logger       *logrus.Logger
}

// NewQuestionHandler creates a new question handler
func NewQuestionHandler(questionRepo repository.QuestionRepositoryInterface, contentRepo repository.ContentRepositoryInterface, logger *logrus.Logger) *QuestionHandler {
	return &QuestionHandler{
		questionRepo: questionRepo,
		contentRepo:  contentRepo,
		logger:       logger,
	}
}

// QueryQuestions handles POST /v1/questions/query
func (h *QuestionHandler) QueryQuestions(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
	defer cancel()

	// Parse request body
	var req models.QuestionQueryRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.logger.WithError(err).Error("Failed to decode request body")
		h.writeErrorResponse(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if err := h.validateQueryRequest(&req); err != nil {
		h.logger.WithError(err).Error("Request validation failed")
		h.writeErrorResponse(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Log the query request
	h.logger.WithFields(logrus.Fields{
		"skill_ids":            req.SkillIDs,
		"target_difficulty":    req.TargetDifficulty,
		"difficulty_range":     req.DifficultyRange,
		"exclude_question_ids": req.ExcludeQuestionIDs,
		"question_type":        req.QuestionType,
		"strategy":             req.Strategy,
	}).Info("Processing question query request")

	// Find question
	response, err := h.questionRepo.FindQuestion(ctx, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to find question")
		if err.Error() == "no question found matching criteria" {
			h.writeErrorResponse(w, "No questions available matching the specified criteria", http.StatusNotFound)
		} else {
			h.writeErrorResponse(w, "Internal server error", http.StatusInternalServerError)
		}
		return
	}

	// Log successful query
	h.logger.WithFields(logrus.Fields{
		"question_version_id": response.QuestionVersionID,
		"difficulty":          response.Difficulty,
		"skill_count":         len(response.SkillIDs),
	}).Info("Question query completed successfully")

	// Write response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.WithError(err).Error("Failed to encode response")
	}
}

// GetQuestion handles GET /v1/questions/{questionId}
func (h *QuestionHandler) GetQuestion(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
	defer cancel()

	// Parse question ID from URL
	vars := mux.Vars(r)
	questionIDStr, ok := vars["questionId"]
	if !ok {
		h.writeErrorResponse(w, "Question ID is required", http.StatusBadRequest)
		return
	}

	questionID, err := uuid.Parse(questionIDStr)
	if err != nil {
		h.logger.WithError(err).WithField("question_id", questionIDStr).Error("Invalid question ID format")
		h.writeErrorResponse(w, "Invalid question ID format", http.StatusBadRequest)
		return
	}

	// Get question details
	response, err := h.questionRepo.GetQuestionByID(ctx, questionID)
	if err != nil {
		h.logger.WithError(err).WithField("question_id", questionID).Error("Failed to get question")
		if err.Error() == "question not found" {
			h.writeErrorResponse(w, "Question not found", http.StatusNotFound)
		} else {
			h.writeErrorResponse(w, "Internal server error", http.StatusInternalServerError)
		}
		return
	}

	// Write response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.WithError(err).Error("Failed to encode response")
	}
}

// CreateQuestion handles POST /v1/questions
func (h *QuestionHandler) CreateQuestion(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
	defer cancel()

	// Parse request body
	var req models.CreateQuestionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.logger.WithError(err).Error("Failed to decode request body")
		h.writeErrorResponse(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if err := h.validateCreateQuestionRequest(&req); err != nil {
		h.logger.WithError(err).Error("Request validation failed")
		h.writeErrorResponse(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Create question
	response, err := h.contentRepo.CreateQuestion(ctx, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create question")
		h.writeErrorResponse(w, "Failed to create question", http.StatusInternalServerError)
		return
	}

	// Write response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.WithError(err).Error("Failed to encode response")
	}
}

// CreateQuestionVersion handles PUT /v1/questions/{questionId}/versions
func (h *QuestionHandler) CreateQuestionVersion(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
	defer cancel()

	// Parse question ID from URL
	vars := mux.Vars(r)
	questionIDStr, ok := vars["questionId"]
	if !ok {
		h.writeErrorResponse(w, "Question ID is required", http.StatusBadRequest)
		return
	}

	questionID, err := uuid.Parse(questionIDStr)
	if err != nil {
		h.logger.WithError(err).WithField("question_id", questionIDStr).Error("Invalid question ID format")
		h.writeErrorResponse(w, "Invalid question ID format", http.StatusBadRequest)
		return
	}

	// Parse request body
	var req models.CreateQuestionVersionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.logger.WithError(err).Error("Failed to decode request body")
		h.writeErrorResponse(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if err := h.validateCreateQuestionVersionRequest(&req); err != nil {
		h.logger.WithError(err).Error("Request validation failed")
		h.writeErrorResponse(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Create question version
	response, err := h.contentRepo.CreateQuestionVersion(ctx, questionID, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create question version")
		h.writeErrorResponse(w, "Failed to create question version", http.StatusInternalServerError)
		return
	}

	// Write response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.WithError(err).Error("Failed to encode response")
	}
}

// validateQueryRequest validates the question query request
func (h *QuestionHandler) validateQueryRequest(req *models.QuestionQueryRequest) error {
	// Validate difficulty range
	if req.DifficultyRange != nil {
		if req.DifficultyRange.Min != nil && (*req.DifficultyRange.Min < -10.0 || *req.DifficultyRange.Min > 10.0) {
			return fmt.Errorf("difficulty range min must be between -10.0 and 10.0")
		}
		if req.DifficultyRange.Max != nil && (*req.DifficultyRange.Max < -10.0 || *req.DifficultyRange.Max > 10.0) {
			return fmt.Errorf("difficulty range max must be between -10.0 and 10.0")
		}
		if req.DifficultyRange.Min != nil && req.DifficultyRange.Max != nil && *req.DifficultyRange.Min > *req.DifficultyRange.Max {
			return fmt.Errorf("difficulty range min cannot be greater than max")
		}
	}

	// Validate target difficulty
	if req.TargetDifficulty != nil && (*req.TargetDifficulty < -10.0 || *req.TargetDifficulty > 10.0) {
		return fmt.Errorf("target difficulty must be between -10.0 and 10.0")
	}

	return nil
}

// validateCreateQuestionRequest validates the create question request
func (h *QuestionHandler) validateCreateQuestionRequest(req *models.CreateQuestionRequest) error {
	if req.Text == "" {
		return fmt.Errorf("question text is required")
	}
	if len(req.SkillIDs) == 0 {
		return fmt.Errorf("at least one skill ID is required")
	}
	if req.Difficulty != nil && (*req.Difficulty < -10.0 || *req.Difficulty > 10.0) {
		return fmt.Errorf("difficulty must be between -10.0 and 10.0")
	}
	return nil
}

// validateCreateQuestionVersionRequest validates the create question version request
func (h *QuestionHandler) validateCreateQuestionVersionRequest(req *models.CreateQuestionVersionRequest) error {
	if req.Text == "" {
		return fmt.Errorf("question text is required")
	}
	if req.Difficulty != nil && (*req.Difficulty < -10.0 || *req.Difficulty > 10.0) {
		return fmt.Errorf("difficulty must be between -10.0 and 10.0")
	}
	return nil
}

// writeErrorResponse writes an error response
func (h *QuestionHandler) writeErrorResponse(w http.ResponseWriter, message string, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	errorResponse := models.ErrorResponse{
		Error:   http.StatusText(statusCode),
		Message: message,
		Code:    statusCode,
	}

	if err := json.NewEncoder(w).Encode(errorResponse); err != nil {
		h.logger.WithError(err).Error("Failed to encode error response")
	}
}
