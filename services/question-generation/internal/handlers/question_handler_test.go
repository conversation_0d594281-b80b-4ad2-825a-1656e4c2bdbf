package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"question-generation-service/internal/models"
)

// MockQuestionRepository is a mock implementation of QuestionRepository
type MockQuestionRepository struct {
	mock.Mock
}

func (m *MockQuestionRepository) FindQuestion(ctx context.Context, req *models.QuestionQueryRequest) (*models.QuestionQueryResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.QuestionQueryResponse), args.Error(1)
}

func (m *MockQuestionRepository) GetQuestionByID(ctx context.Context, questionID uuid.UUID) (*models.QuestionDetailResponse, error) {
	args := m.Called(ctx, questionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.QuestionDetailResponse), args.Error(1)
}

// MockContentRepository is a mock implementation of ContentRepository
type MockContentRepository struct {
	mock.Mock
}

func (m *MockContentRepository) CreateQuestion(ctx context.Context, req *models.CreateQuestionRequest) (*models.CreateQuestionResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.CreateQuestionResponse), args.Error(1)
}

func (m *MockContentRepository) CreateQuestionVersion(ctx context.Context, questionID uuid.UUID, req *models.CreateQuestionVersionRequest) (*models.CreateQuestionVersionResponse, error) {
	args := m.Called(ctx, questionID, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.CreateQuestionVersionResponse), args.Error(1)
}

func (m *MockContentRepository) CreateSkill(ctx context.Context, req *models.CreateSkillRequest) (*models.CreateSkillResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.CreateSkillResponse), args.Error(1)
}

func (m *MockContentRepository) GetSkills(ctx context.Context, subjectID, domainID *uuid.UUID) (*models.SkillListResponse, error) {
	args := m.Called(ctx, subjectID, domainID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.SkillListResponse), args.Error(1)
}

func TestQuestionHandler_QueryQuestions(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel) // Suppress logs during tests

	questionID := uuid.New()
	skillID := uuid.New()

	tests := []struct {
		name           string
		requestBody    interface{}
		setupMocks     func(*MockQuestionRepository, *MockContentRepository)
		expectedStatus int
		validateResp   func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "successful query",
			requestBody: models.QuestionQueryRequest{
				SkillIDs:         []uuid.UUID{skillID},
				TargetDifficulty: func() *float64 { f := 0.5; return &f }(),
			},
			setupMocks: func(qr *MockQuestionRepository, cr *MockContentRepository) {
				qr.On("FindQuestion", mock.Anything, mock.AnythingOfType("*models.QuestionQueryRequest")).Return(&models.QuestionQueryResponse{
					QuestionVersionID: questionID,
					QuestionText:      "What is 2+2?",
					Options:           []string{"3", "4", "5", "6"},
					SkillIDs:          []uuid.UUID{skillID},
					Difficulty:        0.5,
					AssetURLs:         []string{},
					Explanation:       func() *string { s := "Basic addition"; return &s }(),
					Metadata: map[string]interface{}{
						"type":           "multiple_choice",
						"correct_answer": "4",
					},
				}, nil)
			},
			expectedStatus: http.StatusOK,
			validateResp: func(t *testing.T, w *httptest.ResponseRecorder) {
				var resp models.QuestionQueryResponse
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				require.NoError(t, err)
				assert.Equal(t, questionID, resp.QuestionVersionID)
				assert.Equal(t, "What is 2+2?", resp.QuestionText)
				assert.Equal(t, 0.5, resp.Difficulty)
			},
		},
		{
			name:           "invalid request body",
			requestBody:    "invalid json",
			setupMocks:     func(qr *MockQuestionRepository, cr *MockContentRepository) {},
			expectedStatus: http.StatusBadRequest,
			validateResp: func(t *testing.T, w *httptest.ResponseRecorder) {
				var resp models.ErrorResponse
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				require.NoError(t, err)
				assert.Equal(t, "Invalid request body", resp.Message)
			},
		},
		{
			name: "validation error",
			requestBody: models.QuestionQueryRequest{
				TargetDifficulty: func() *float64 { f := 15.0; return &f }(), // Invalid difficulty
			},
			setupMocks:     func(qr *MockQuestionRepository, cr *MockContentRepository) {},
			expectedStatus: http.StatusBadRequest,
			validateResp: func(t *testing.T, w *httptest.ResponseRecorder) {
				var resp models.ErrorResponse
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				require.NoError(t, err)
				assert.Contains(t, resp.Message, "difficulty must be between")
			},
		},
		{
			name: "no questions found",
			requestBody: models.QuestionQueryRequest{
				SkillIDs: []uuid.UUID{skillID},
			},
			setupMocks: func(qr *MockQuestionRepository, cr *MockContentRepository) {
				qr.On("FindQuestion", mock.Anything, mock.AnythingOfType("*models.QuestionQueryRequest")).Return(nil, fmt.Errorf("no question found matching criteria"))
			},
			expectedStatus: http.StatusNotFound,
			validateResp: func(t *testing.T, w *httptest.ResponseRecorder) {
				var resp models.ErrorResponse
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				require.NoError(t, err)
				assert.Contains(t, resp.Message, "No questions available")
			},
		},
		{
			name: "internal server error",
			requestBody: models.QuestionQueryRequest{
				SkillIDs: []uuid.UUID{skillID},
			},
			setupMocks: func(qr *MockQuestionRepository, cr *MockContentRepository) {
				qr.On("FindQuestion", mock.Anything, mock.AnythingOfType("*models.QuestionQueryRequest")).Return(nil, fmt.Errorf("database connection failed"))
			},
			expectedStatus: http.StatusInternalServerError,
			validateResp: func(t *testing.T, w *httptest.ResponseRecorder) {
				var resp models.ErrorResponse
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				require.NoError(t, err)
				assert.Equal(t, "Internal server error", resp.Message)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockQuestionRepo := new(MockQuestionRepository)
			mockContentRepo := new(MockContentRepository)
			tt.setupMocks(mockQuestionRepo, mockContentRepo)

			// Create handler
			handler := NewQuestionHandler(mockQuestionRepo, mockContentRepo, logger)

			// Create request
			var body []byte
			var err error
			if str, ok := tt.requestBody.(string); ok {
				body = []byte(str)
			} else {
				body, err = json.Marshal(tt.requestBody)
				require.NoError(t, err)
			}

			req := httptest.NewRequest(http.MethodPost, "/v1/questions/query", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// Create response recorder
			w := httptest.NewRecorder()

			// Execute request
			handler.QueryQuestions(w, req)

			// Validate response
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.validateResp != nil {
				tt.validateResp(t, w)
			}

			// Verify mock expectations
			mockQuestionRepo.AssertExpectations(t)
			mockContentRepo.AssertExpectations(t)
		})
	}
}

func TestQuestionHandler_GetQuestion(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)

	questionID := uuid.New()
	skillID := uuid.New()

	tests := []struct {
		name           string
		questionID     string
		setupMocks     func(*MockQuestionRepository, *MockContentRepository)
		expectedStatus int
		validateResp   func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name:       "successful retrieval",
			questionID: questionID.String(),
			setupMocks: func(qr *MockQuestionRepository, cr *MockContentRepository) {
				qr.On("GetQuestionByID", mock.Anything, questionID).Return(&models.QuestionDetailResponse{
					Question: models.Question{
						ID:        questionID,
						SubjectID: func() *uuid.UUID { id := uuid.New(); return &id }(),
						Title:     func() *string { s := "Test Question"; return &s }(),
						CreatedAt: time.Now(),
					},
					CurrentVersion: models.QuestionVersion{
						ID:         questionID,
						QuestionID: questionID,
						Text:       "What is the capital of France?",
						Status:     models.QuestionStatusActive,
						CreatedAt:  time.Now(),
					},
					Skills: []models.Skill{
						{
							ID:            skillID,
							SkillDomainID: uuid.New(),
							Name:          "Geography",
							Description:   func() *string { s := "Geography skills"; return &s }(),
						},
					},
				}, nil)
			},
			expectedStatus: http.StatusOK,
			validateResp: func(t *testing.T, w *httptest.ResponseRecorder) {
				var resp models.QuestionDetailResponse
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				require.NoError(t, err)
				assert.Equal(t, questionID, resp.Question.ID)
				assert.Equal(t, "What is the capital of France?", resp.CurrentVersion.Text)
			},
		},
		{
			name:           "invalid UUID",
			questionID:     "invalid-uuid",
			setupMocks:     func(qr *MockQuestionRepository, cr *MockContentRepository) {},
			expectedStatus: http.StatusBadRequest,
			validateResp: func(t *testing.T, w *httptest.ResponseRecorder) {
				var resp models.ErrorResponse
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				require.NoError(t, err)
				assert.Contains(t, resp.Message, "Invalid question ID format")
			},
		},
		{
			name:       "question not found",
			questionID: questionID.String(),
			setupMocks: func(qr *MockQuestionRepository, cr *MockContentRepository) {
				qr.On("GetQuestionByID", mock.Anything, questionID).Return(nil, fmt.Errorf("question not found"))
			},
			expectedStatus: http.StatusNotFound,
			validateResp: func(t *testing.T, w *httptest.ResponseRecorder) {
				var resp models.ErrorResponse
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				require.NoError(t, err)
				assert.Equal(t, "Question not found", resp.Message)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockQuestionRepo := new(MockQuestionRepository)
			mockContentRepo := new(MockContentRepository)
			tt.setupMocks(mockQuestionRepo, mockContentRepo)

			// Create handler
			handler := NewQuestionHandler(mockQuestionRepo, mockContentRepo, logger)

			// Create request with URL parameters
			req := httptest.NewRequest(http.MethodGet, "/v1/questions/"+tt.questionID, nil)
			req = mux.SetURLVars(req, map[string]string{"questionId": tt.questionID})

			// Create response recorder
			w := httptest.NewRecorder()

			// Execute request
			handler.GetQuestion(w, req)

			// Validate response
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.validateResp != nil {
				tt.validateResp(t, w)
			}

			// Verify mock expectations
			mockQuestionRepo.AssertExpectations(t)
			mockContentRepo.AssertExpectations(t)
		})
	}
}
