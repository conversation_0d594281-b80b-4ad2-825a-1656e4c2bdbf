package repository

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"question-generation-service/internal/models"
)

// BenchmarkQuestionRepository_FindQuestion benchmarks the FindQuestion method
func BenchmarkQuestionRepository_FindQuestion(b *testing.B) {
	// Create mock database
	db, mock, err := sqlmock.New()
	if err != nil {
		b.<PERSON>al(err)
	}
	defer db.Close()

	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel) // Suppress logs during benchmarks
	dbWrapper := newTestDB(db, logger)
	repo := NewQuestionRepository(dbWrapper, logger)

	questionID := uuid.New()
	skillID1 := uuid.New()
	skillID2 := uuid.New()

	// Setup mock expectations for all benchmark iterations
	for i := 0; i < b.N; i++ {
		rows := sqlmock.NewRows([]string{
			"id", "text", "difficulty", "skill_ids", "asset_urls", "explanation", "metadata",
		}).AddRow(
			questionID.String(),
			"What is 2+2?",
			0.5,
			`{`+skillID1.String()+`,`+skillID2.String()+`}`,
			`{https://example.com/image.png}`,
			"Basic addition",
			`{"type":"multiple_choice","options":["3","4","5","6"],"correct_answer":"4"}`,
		)

		mock.ExpectQuery(`SELECT DISTINCT qv\.id, qv\.text, COALESCE\(irt\.difficulty, 0\.5\) as difficulty`).
			WithArgs(skillID1, skillID2, 0.5).
			WillReturnRows(rows)
	}

	request := &models.QuestionQueryRequest{
		SkillIDs:         []uuid.UUID{skillID1, skillID2},
		TargetDifficulty: func() *float64 { f := 0.5; return &f }(),
	}

	ctx := context.Background()

	// Reset timer to exclude setup time
	b.ResetTimer()

	// Run benchmark
	for i := 0; i < b.N; i++ {
		_, err := repo.FindQuestion(ctx, request)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkQuestionRepository_GetQuestionByID benchmarks the GetQuestionByID method
func BenchmarkQuestionRepository_GetQuestionByID(b *testing.B) {
	// Create mock database
	db, mock, err := sqlmock.New()
	if err != nil {
		b.Fatal(err)
	}
	defer db.Close()

	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	dbWrapper := newTestDB(db, logger)
	repo := NewQuestionRepository(dbWrapper, logger)

	questionID := uuid.New()
	skillID := uuid.New()

	// Setup mock expectations for all benchmark iterations
	for i := 0; i < b.N; i++ {
		rows := sqlmock.NewRows([]string{
			"id", "text", "difficulty", "skill_ids", "asset_urls", "explanation", "metadata",
		}).AddRow(
			questionID.String(),
			"What is the capital of France?",
			0.3,
			`{`+skillID.String()+`}`,
			`{https://example.com/map.png}`,
			"Geography question",
			`{"type":"multiple_choice","options":["London","Paris","Berlin","Madrid"],"correct_answer":"Paris"}`,
		)

		mock.ExpectQuery(`SELECT qv\.id, qv\.text, COALESCE\(irt\.difficulty, 0\.5\) as difficulty`).
			WithArgs(questionID).
			WillReturnRows(rows)
	}

	ctx := context.Background()

	// Reset timer to exclude setup time
	b.ResetTimer()

	// Run benchmark
	for i := 0; i < b.N; i++ {
		_, err := repo.GetQuestionByID(ctx, questionID)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkContentRepository_CreateQuestion benchmarks the CreateQuestion method
func BenchmarkContentRepository_CreateQuestion(b *testing.B) {
	// Create mock database
	db, mock, err := sqlmock.New()
	if err != nil {
		b.Fatal(err)
	}
	defer db.Close()

	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	dbWrapper := newTestDB(db, logger)
	repo := NewContentRepository(dbWrapper, logger)

	questionID := uuid.New()
	versionID := uuid.New()
	subjectID := uuid.New()
	skillID1 := uuid.New()
	skillID2 := uuid.New()

	// Setup mock expectations for all benchmark iterations
	for i := 0; i < b.N; i++ {
		// Begin transaction
		mock.ExpectBegin()

		// Insert question
		mock.ExpectQuery(`INSERT INTO content\.questions`).
			WithArgs(subjectID, "Benchmark Question", sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(questionID))

		// Insert question version
		mock.ExpectQuery(`INSERT INTO content\.question_versions`).
			WithArgs(questionID, "What is 2+2?", models.QuestionStatusActive, sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(versionID))

		// Insert skill mappings
		mock.ExpectExec(`INSERT INTO content\.question_skill_mapping`).
			WithArgs(versionID, skillID1).
			WillReturnResult(sqlmock.NewResult(1, 1))

		mock.ExpectExec(`INSERT INTO content\.question_skill_mapping`).
			WithArgs(versionID, skillID2).
			WillReturnResult(sqlmock.NewResult(1, 1))

		// Insert asset
		mock.ExpectExec(`INSERT INTO content\.question_assets`).
			WithArgs(versionID, "https://example.com/image.png").
			WillReturnResult(sqlmock.NewResult(1, 1))

		// Insert IRT parameters
		mock.ExpectExec(`INSERT INTO models\.item_response_theory_parameters`).
			WithArgs(versionID, models.IRTModel2PL, 0.5, 1.0, 0.0).
			WillReturnResult(sqlmock.NewResult(1, 1))

		// Commit transaction
		mock.ExpectCommit()
	}

	request := &models.CreateQuestionRequest{
		SubjectID:  &subjectID,
		Title:      func() *string { s := "Benchmark Question"; return &s }(),
		Text:       "What is 2+2?",
		SkillIDs:   []uuid.UUID{skillID1, skillID2},
		Difficulty: func() *float64 { f := 0.5; return &f }(),
		AssetURLs:  []string{"https://example.com/image.png"},
		Metadata: map[string]interface{}{
			"type":           "multiple_choice",
			"options":        []string{"3", "4", "5", "6"},
			"correct_answer": "4",
		},
	}

	ctx := context.Background()

	// Reset timer to exclude setup time
	b.ResetTimer()

	// Run benchmark
	for i := 0; i < b.N; i++ {
		_, err := repo.CreateQuestion(ctx, request)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkValidation benchmarks the validation functions
func BenchmarkValidation(b *testing.B) {
	skillID1 := uuid.New()
	skillID2 := uuid.New()

	b.Run("ValidateQuestionQueryRequest", func(b *testing.B) {
		request := &models.QuestionQueryRequest{
			SkillIDs:         []uuid.UUID{skillID1, skillID2},
			TargetDifficulty: func() *float64 { f := 0.5; return &f }(),
			DifficultyRange: &models.DifficultyRange{
				Min: func() *float64 { f := 0.3; return &f }(),
				Max: func() *float64 { f := 0.7; return &f }(),
			},
			ExcludeQuestionIDs: []uuid.UUID{uuid.New(), uuid.New()},
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// Import validation package would be needed here
			// validation.ValidateQuestionQueryRequest(request)
			_ = request // Placeholder to avoid unused variable error
		}
	})

	b.Run("ValidateCreateQuestionRequest", func(b *testing.B) {
		subjectID := uuid.New()
		request := &models.CreateQuestionRequest{
			SubjectID:  &subjectID,
			Title:      func() *string { s := "Benchmark Question"; return &s }(),
			Text:       "What is 2+2?",
			SkillIDs:   []uuid.UUID{skillID1, skillID2},
			Difficulty: func() *float64 { f := 0.5; return &f }(),
			AssetURLs:  []string{"https://example.com/image.png"},
			Metadata: map[string]interface{}{
				"type":           "multiple_choice",
				"options":        []string{"3", "4", "5", "6"},
				"correct_answer": "4",
			},
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// Import validation package would be needed here
			// validation.ValidateCreateQuestionRequest(request)
			_ = request // Placeholder to avoid unused variable error
		}
	})
}

// BenchmarkJSONMarshaling benchmarks JSON marshaling/unmarshaling of models
func BenchmarkJSONMarshaling(b *testing.B) {
	questionID := uuid.New()
	skillID := uuid.New()

	response := &models.QuestionQueryResponse{
		QuestionVersionID: questionID,
		QuestionText:      "What is 2+2?",
		Options:           []string{"3", "4", "5", "6"},
		SkillIDs:          []uuid.UUID{skillID},
		Difficulty:        0.5,
		AssetURLs:         []string{"https://example.com/image.png"},
		Explanation:       func() *string { s := "Basic addition"; return &s }(),
		Metadata: map[string]interface{}{
			"type":           "multiple_choice",
			"correct_answer": "4",
		},
	}

	b.Run("Marshal", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := json.Marshal(response)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	// Get marshaled data for unmarshal benchmark
	data, err := json.Marshal(response)
	if err != nil {
		b.Fatal(err)
	}

	b.Run("Unmarshal", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			var resp models.QuestionQueryResponse
			err := json.Unmarshal(data, &resp)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}
