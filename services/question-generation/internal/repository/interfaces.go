package repository

import (
	"context"

	"github.com/google/uuid"

	"question-generation-service/internal/models"
)

// QuestionRepositoryInterface defines the interface for question repository operations
type QuestionRepositoryInterface interface {
	FindQuestion(ctx context.Context, req *models.QuestionQueryRequest) (*models.QuestionQueryResponse, error)
	GetQuestionByID(ctx context.Context, questionID uuid.UUID) (*models.QuestionDetailResponse, error)
}

// ContentRepositoryInterface defines the interface for content repository operations
type ContentRepositoryInterface interface {
	CreateQuestion(ctx context.Context, req *models.CreateQuestionRequest) (*models.CreateQuestionResponse, error)
	CreateQuestionVersion(ctx context.Context, questionID uuid.UUID, req *models.CreateQuestionVersionRequest) (*models.CreateQuestionVersionResponse, error)
	CreateSkill(ctx context.Context, req *models.CreateSkillRequest) (*models.CreateSkillResponse, error)
	GetSkills(ctx context.Context, subjectID, domainID *uuid.UUID) (*models.SkillListResponse, error)
}
