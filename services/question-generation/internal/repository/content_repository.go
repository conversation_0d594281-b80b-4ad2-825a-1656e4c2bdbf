package repository

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"question-generation-service/internal/database"
	"question-generation-service/internal/models"
)

// ContentRepository handles database operations for content management
type ContentRepository struct {
	db     *database.DB
	logger *logrus.Logger
}

// NewContentRepository creates a new content repository
func NewContentRepository(db *database.DB, logger *logrus.Logger) *ContentRepository {
	return &ContentRepository{
		db:     db,
		logger: logger,
	}
}

// CreateQuestion creates a new question with its initial version
func (r *ContentRepository) CreateQuestion(ctx context.Context, req *models.CreateQuestionRequest) (*models.CreateQuestionResponse, error) {
	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Create the question
	questionID := uuid.New()
	questionQuery := `
		INSERT INTO content.questions (id, subject_id, title, created_at)
		VALUES ($1, $2, $3, $4)
	`

	_, err = tx.ExecContext(ctx, questionQuery, questionID, req.SubjectID, req.Title, time.Now())
	if err != nil {
		return nil, fmt.Errorf("failed to create question: %w", err)
	}

	// Create the initial version
	versionID := uuid.New()
	versionQuery := `
		INSERT INTO content.question_versions (id, question_id, version_number, status, text, metadata, created_at)
		VALUES ($1, $2, 1, 'active', $3, $4, $5)
	`

	_, err = tx.ExecContext(ctx, versionQuery, versionID, questionID, req.Text, req.Metadata, time.Now())
	if err != nil {
		return nil, fmt.Errorf("failed to create question version: %w", err)
	}

	// Create skill mappings
	for _, skillID := range req.SkillIDs {
		mappingQuery := `
			INSERT INTO content.question_skill_mapping (question_version_id, skill_id)
			VALUES ($1, $2)
		`
		_, err = tx.ExecContext(ctx, mappingQuery, versionID, skillID)
		if err != nil {
			return nil, fmt.Errorf("failed to create skill mapping: %w", err)
		}
	}

	// Create asset records if provided
	for _, assetURL := range req.AssetURLs {
		assetQuery := `
			INSERT INTO content.question_assets (id, question_version_id, asset_url)
			VALUES ($1, $2, $3)
		`
		_, err = tx.ExecContext(ctx, assetQuery, uuid.New(), versionID, assetURL)
		if err != nil {
			return nil, fmt.Errorf("failed to create asset: %w", err)
		}
	}

	// Create IRT parameters if difficulty is provided
	if req.Difficulty != nil {
		irtQuery := `
			INSERT INTO models.item_response_theory_parameters 
			(id, question_version_id, model_type, difficulty, discrimination, creation_date)
			VALUES ($1, $2, '2PL', $3, 1.0, $4)
		`
		_, err = tx.ExecContext(ctx, irtQuery, uuid.New(), versionID, *req.Difficulty, time.Now())
		if err != nil {
			return nil, fmt.Errorf("failed to create IRT parameters: %w", err)
		}
	}

	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	r.logger.WithFields(logrus.Fields{
		"question_id":         questionID,
		"question_version_id": versionID,
		"skill_count":         len(req.SkillIDs),
	}).Info("Question created successfully")

	return &models.CreateQuestionResponse{
		QuestionID:        questionID,
		QuestionVersionID: versionID,
		Message:           "Question created successfully",
	}, nil
}

// CreateQuestionVersion creates a new version for an existing question
func (r *ContentRepository) CreateQuestionVersion(ctx context.Context, questionID uuid.UUID, req *models.CreateQuestionVersionRequest) (*models.CreateQuestionVersionResponse, error) {
	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Get the next version number
	versionQuery := `
		SELECT COALESCE(MAX(version_number), 0) + 1
		FROM content.question_versions
		WHERE question_id = $1
	`

	var nextVersion int
	err = tx.QueryRowContext(ctx, versionQuery, questionID).Scan(&nextVersion)
	if err != nil {
		return nil, fmt.Errorf("failed to get next version number: %w", err)
	}

	// Archive the current active version
	archiveQuery := `
		UPDATE content.question_versions
		SET status = 'archived'
		WHERE question_id = $1 AND status = 'active'
	`

	_, err = tx.ExecContext(ctx, archiveQuery, questionID)
	if err != nil {
		return nil, fmt.Errorf("failed to archive current version: %w", err)
	}

	// Create the new version
	versionID := uuid.New()
	createVersionQuery := `
		INSERT INTO content.question_versions (id, question_id, version_number, status, text, metadata, created_at)
		VALUES ($1, $2, $3, 'active', $4, $5, $6)
	`

	_, err = tx.ExecContext(ctx, createVersionQuery, versionID, questionID, nextVersion, req.Text, req.Metadata, time.Now())
	if err != nil {
		return nil, fmt.Errorf("failed to create question version: %w", err)
	}

	// Copy skill mappings from the previous version
	copySkillsQuery := `
		INSERT INTO content.question_skill_mapping (question_version_id, skill_id)
		SELECT $1, skill_id
		FROM content.question_skill_mapping
		WHERE question_version_id = (
			SELECT id FROM content.question_versions
			WHERE question_id = $2 AND status = 'archived'
			ORDER BY version_number DESC
			LIMIT 1
		)
	`

	_, err = tx.ExecContext(ctx, copySkillsQuery, versionID, questionID)
	if err != nil {
		return nil, fmt.Errorf("failed to copy skill mappings: %w", err)
	}

	// Create asset records if provided
	for _, assetURL := range req.AssetURLs {
		assetQuery := `
			INSERT INTO content.question_assets (id, question_version_id, asset_url)
			VALUES ($1, $2, $3)
		`
		_, err = tx.ExecContext(ctx, assetQuery, uuid.New(), versionID, assetURL)
		if err != nil {
			return nil, fmt.Errorf("failed to create asset: %w", err)
		}
	}

	// Create IRT parameters if difficulty is provided
	if req.Difficulty != nil {
		irtQuery := `
			INSERT INTO models.item_response_theory_parameters 
			(id, question_version_id, model_type, difficulty, discrimination, creation_date)
			VALUES ($1, $2, '2PL', $3, 1.0, $4)
		`
		_, err = tx.ExecContext(ctx, irtQuery, uuid.New(), versionID, *req.Difficulty, time.Now())
		if err != nil {
			return nil, fmt.Errorf("failed to create IRT parameters: %w", err)
		}
	}

	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	r.logger.WithFields(logrus.Fields{
		"question_id":         questionID,
		"question_version_id": versionID,
		"version_number":      nextVersion,
	}).Info("Question version created successfully")

	return &models.CreateQuestionVersionResponse{
		QuestionVersionID: versionID,
		VersionNumber:     nextVersion,
		Message:           "Question version created successfully",
	}, nil
}

// CreateSkill creates a new skill
func (r *ContentRepository) CreateSkill(ctx context.Context, req *models.CreateSkillRequest) (*models.CreateSkillResponse, error) {
	skillID := uuid.New()
	query := `
		INSERT INTO content.skills (id, skill_domain_id, name, description)
		VALUES ($1, $2, $3, $4)
	`

	_, err := r.db.ExecContext(ctx, query, skillID, req.SkillDomainID, req.Name, req.Description)
	if err != nil {
		return nil, fmt.Errorf("failed to create skill: %w", err)
	}

	r.logger.WithFields(logrus.Fields{
		"skill_id":        skillID,
		"skill_domain_id": req.SkillDomainID,
		"name":            req.Name,
	}).Info("Skill created successfully")

	return &models.CreateSkillResponse{
		SkillID: skillID,
		Message: "Skill created successfully",
	}, nil
}

// GetSkills retrieves skills with optional filtering
func (r *ContentRepository) GetSkills(ctx context.Context, subjectID *uuid.UUID, domainID *uuid.UUID) (*models.SkillListResponse, error) {
	query := `
		SELECT s.id, s.skill_domain_id, s.name, s.description,
		       sd.id, sd.subject_id, sd.name, sd.description,
		       sub.id, sub.name, sub.description
		FROM content.skills s
		JOIN content.skill_domains sd ON sd.id = s.skill_domain_id
		JOIN content.subjects sub ON sub.id = sd.subject_id
	`

	var conditions []string
	var args []any
	argIndex := 1

	if subjectID != nil {
		conditions = append(conditions, fmt.Sprintf("sub.id = $%d", argIndex))
		args = append(args, *subjectID)
		argIndex++
	}

	if domainID != nil {
		conditions = append(conditions, fmt.Sprintf("sd.id = $%d", argIndex))
		args = append(args, *domainID)
		argIndex++
	}

	if len(conditions) > 0 {
		query += " WHERE " + conditions[0]
		for i := 1; i < len(conditions); i++ {
			query += " AND " + conditions[i]
		}
	}

	query += " ORDER BY sub.name, sd.name, s.name"

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get skills: %w", err)
	}
	defer rows.Close()

	var skills []models.SkillWithDomain
	for rows.Next() {
		var skill models.SkillWithDomain
		var skillDesc, domainDesc, subjectDesc sql.NullString

		err := rows.Scan(
			&skill.Skill.ID,
			&skill.Skill.SkillDomainID,
			&skill.Skill.Name,
			&skillDesc,
			&skill.SkillDomain.ID,
			&skill.SkillDomain.SubjectID,
			&skill.SkillDomain.Name,
			&domainDesc,
			&skill.Subject.ID,
			&skill.Subject.Name,
			&subjectDesc,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan skill: %w", err)
		}

		if skillDesc.Valid {
			skill.Skill.Description = &skillDesc.String
		}
		if domainDesc.Valid {
			skill.SkillDomain.Description = &domainDesc.String
		}
		if subjectDesc.Valid {
			skill.Subject.Description = &subjectDesc.String
		}

		skills = append(skills, skill)
	}

	return &models.SkillListResponse{
		Skills: skills,
		Total:  len(skills),
	}, nil
}
