package repository

import (
	"context"
	"database/sql"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"question-generation-service/internal/models"
)

func TestContentRepository_CreateQuestion(t *testing.T) {
	// Create mock database
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	logger := logrus.New()
	dbWrapper := newTestDB(db, logger)
	repo := NewContentRepository(dbWrapper, logger)

	subjectID := uuid.New()
	skillID1 := uuid.New()
	skillID2 := uuid.New()

	tests := []struct {
		name        string
		request     *models.CreateQuestionRequest
		setupMock   func()
		expectedErr string
		validate    func(*models.CreateQuestionResponse)
	}{
		{
			name: "successful question creation",
			request: &models.CreateQuestionRequest{
				SubjectID:  &subjectID,
				Title:      func() *string { s := "Math Question"; return &s }(),
				Text:       "What is 2+2?",
				SkillIDs:   []uuid.UUID{skillID1, skillID2},
				Difficulty: func() *float64 { f := 0.5; return &f }(),
				AssetURLs:  []string{"https://example.com/image.png"},
				Metadata: map[string]interface{}{
					"type":           "multiple_choice",
					"options":        []string{"3", "4", "5", "6"},
					"correct_answer": "4",
				},
			},
			setupMock: func() {
				// Begin transaction
				mock.ExpectBegin()

				// Insert question
				mock.ExpectExec(`INSERT INTO content\.questions`).
					WithArgs(sqlmock.AnyArg(), subjectID, "Math Question", sqlmock.AnyArg()).
					WillReturnResult(sqlmock.NewResult(1, 1))

				// Insert question version
				mock.ExpectExec(`INSERT INTO content\.question_versions`).
					WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "What is 2+2?", sqlmock.AnyArg(), sqlmock.AnyArg()).
					WillReturnResult(sqlmock.NewResult(1, 1))

				// Insert skill mappings
				mock.ExpectExec(`INSERT INTO content\.question_skill_mapping`).
					WithArgs(sqlmock.AnyArg(), skillID1).
					WillReturnResult(sqlmock.NewResult(1, 1))

				mock.ExpectExec(`INSERT INTO content\.question_skill_mapping`).
					WithArgs(sqlmock.AnyArg(), skillID2).
					WillReturnResult(sqlmock.NewResult(1, 1))

				// Insert asset
				mock.ExpectExec(`INSERT INTO content\.question_assets`).
					WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), "https://example.com/image.png").
					WillReturnResult(sqlmock.NewResult(1, 1))

				// Insert IRT parameters
				mock.ExpectExec(`INSERT INTO models\.item_response_theory_parameters`).
					WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 0.5, sqlmock.AnyArg()).
					WillReturnResult(sqlmock.NewResult(1, 1))

				// Commit transaction
				mock.ExpectCommit()
			},
			validate: func(resp *models.CreateQuestionResponse) {
				assert.NotEqual(t, uuid.Nil, resp.QuestionID)
				assert.NotEqual(t, uuid.Nil, resp.QuestionVersionID)
				assert.Equal(t, "Question created successfully", resp.Message)
			},
		},
		{
			name: "transaction rollback on error",
			request: &models.CreateQuestionRequest{
				Text:     "What is 3+3?",
				SkillIDs: []uuid.UUID{skillID1},
			},
			setupMock: func() {
				// Begin transaction
				mock.ExpectBegin()

				// Insert question fails
				mock.ExpectExec(`INSERT INTO content\.questions`).
					WillReturnError(sql.ErrConnDone)

				// Rollback transaction
				mock.ExpectRollback()
			},
			expectedErr: "failed to create question",
		},
		{
			name: "skill mapping failure",
			request: &models.CreateQuestionRequest{
				Text:     "What is 4+4?",
				SkillIDs: []uuid.UUID{skillID1},
			},
			setupMock: func() {
				// Begin transaction
				mock.ExpectBegin()

				// Insert question
				mock.ExpectExec(`INSERT INTO content\.questions`).
					WillReturnResult(sqlmock.NewResult(1, 1))

				// Insert question version
				mock.ExpectExec(`INSERT INTO content\.question_versions`).
					WillReturnResult(sqlmock.NewResult(1, 1))

				// Insert skill mapping fails
				mock.ExpectExec(`INSERT INTO content\.question_skill_mapping`).
					WithArgs(sqlmock.AnyArg(), skillID1).
					WillReturnError(sql.ErrConnDone)

				// Rollback transaction
				mock.ExpectRollback()
			},
			expectedErr: "failed to create skill mapping",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()

			ctx := context.Background()
			resp, err := repo.CreateQuestion(ctx, tt.request)

			if tt.expectedErr != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr)
				assert.Nil(t, resp)
			} else {
				if err != nil {
					t.Logf("Unexpected error: %v", err)
				}
				assert.NoError(t, err)
				if resp == nil {
					t.Logf("Response is nil")
				}
				assert.NotNil(t, resp)
				if tt.validate != nil && resp != nil {
					tt.validate(resp)
				}
			}

			// Verify all expectations were met
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestContentRepository_CreateSkill(t *testing.T) {
	// Create mock database
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	logger := logrus.New()
	dbWrapper := newTestDB(db, logger)
	repo := NewContentRepository(dbWrapper, logger)

	domainID := uuid.New()

	tests := []struct {
		name        string
		request     *models.CreateSkillRequest
		setupMock   func()
		expectedErr string
		validate    func(*models.CreateSkillResponse)
	}{
		{
			name: "successful skill creation",
			request: &models.CreateSkillRequest{
				SkillDomainID: domainID,
				Name:          "Addition",
				Description:   func() *string { s := "Basic addition skills"; return &s }(),
			},
			setupMock: func() {
				mock.ExpectExec(`INSERT INTO content\.skills`).
					WithArgs(sqlmock.AnyArg(), domainID, "Addition", "Basic addition skills").
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			validate: func(resp *models.CreateSkillResponse) {
				assert.NotEqual(t, uuid.Nil, resp.SkillID)
				assert.Equal(t, "Skill created successfully", resp.Message)
			},
		},
		{
			name: "database error",
			request: &models.CreateSkillRequest{
				SkillDomainID: domainID,
				Name:          "Subtraction",
			},
			setupMock: func() {
				mock.ExpectQuery(`INSERT INTO content\.skills`).
					WillReturnError(sql.ErrConnDone)
			},
			expectedErr: "failed to create skill",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()

			ctx := context.Background()
			resp, err := repo.CreateSkill(ctx, tt.request)

			if tt.expectedErr != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr)
				assert.Nil(t, resp)
			} else {
				if err != nil {
					t.Logf("Unexpected error: %v", err)
				}
				assert.NoError(t, err)
				if resp == nil {
					t.Logf("Response is nil")
				}
				assert.NotNil(t, resp)
				if tt.validate != nil && resp != nil {
					tt.validate(resp)
				}
			}

			// Verify all expectations were met
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestContentRepository_GetSkills(t *testing.T) {
	// Create mock database
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	logger := logrus.New()
	dbWrapper := newTestDB(db, logger)
	repo := NewContentRepository(dbWrapper, logger)

	skillID1 := uuid.New()
	skillID2 := uuid.New()
	domainID := uuid.New()
	subjectID := uuid.New()

	tests := []struct {
		name        string
		subjectID   *uuid.UUID
		domainID    *uuid.UUID
		setupMock   func()
		expectedErr string
		validate    func(*models.SkillListResponse)
	}{
		{
			name: "successful skills retrieval",
			setupMock: func() {
				rows := sqlmock.NewRows([]string{"id", "skill_domain_id", "name", "description"}).
					AddRow(skillID1, domainID, "Addition", "Basic addition skills").
					AddRow(skillID2, domainID, "Subtraction", "Basic subtraction skills")

				mock.ExpectQuery(`SELECT s\.id, s\.skill_domain_id, s\.name, s\.description FROM content\.skills s`).
					WillReturnRows(rows)
			},
			validate: func(resp *models.SkillListResponse) {
				assert.Equal(t, 2, resp.Total)
				assert.Len(t, resp.Skills, 2)
				assert.Equal(t, skillID1, resp.Skills[0].Skill.ID)
				assert.Equal(t, "Addition", resp.Skills[0].Skill.Name)
				assert.Equal(t, skillID2, resp.Skills[1].Skill.ID)
				assert.Equal(t, "Subtraction", resp.Skills[1].Skill.Name)
			},
		},
		{
			name:      "filtered by subject",
			subjectID: &subjectID,
			setupMock: func() {
				rows := sqlmock.NewRows([]string{"id", "skill_domain_id", "name", "description"}).
					AddRow(skillID1, domainID, "Addition", "Basic addition skills")

				mock.ExpectQuery(`SELECT s\.id, s\.skill_domain_id, s\.name, s\.description FROM content\.skills s`).
					WithArgs(subjectID).
					WillReturnRows(rows)
			},
			validate: func(resp *models.SkillListResponse) {
				assert.Equal(t, 1, resp.Total)
				assert.Len(t, resp.Skills, 1)
			},
		},
		{
			name: "database error",
			setupMock: func() {
				mock.ExpectQuery(`SELECT s\.id, s\.skill_domain_id, s\.name, s\.description FROM content\.skills s`).
					WillReturnError(sql.ErrConnDone)
			},
			expectedErr: "failed to get skills",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()

			ctx := context.Background()
			resp, err := repo.GetSkills(ctx, tt.subjectID, tt.domainID)

			if tt.expectedErr != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr)
				assert.Nil(t, resp)
			} else {
				if err != nil {
					t.Logf("Unexpected error: %v", err)
				}
				assert.NoError(t, err)
				if resp == nil {
					t.Logf("Response is nil")
				}
				assert.NotNil(t, resp)
				if tt.validate != nil && resp != nil {
					tt.validate(resp)
				}
			}

			// Verify all expectations were met
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}
