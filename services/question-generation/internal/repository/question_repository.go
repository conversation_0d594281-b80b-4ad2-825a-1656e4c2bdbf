package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"github.com/sirupsen/logrus"

	"question-generation-service/internal/database"
	"question-generation-service/internal/models"
)

// QuestionRepository handles database operations for questions
type QuestionRepository struct {
	db     *database.DB
	logger *logrus.Logger
}

// NewQuestionRepository creates a new question repository
func NewQuestionRepository(db *database.DB, logger *logrus.Logger) *QuestionRepository {
	return &QuestionRepository{
		db:     db,
		logger: logger,
	}
}

// FindQuestion finds a question based on the given criteria
func (r *QuestionRepository) FindQuestion(ctx context.Context, req *models.QuestionQueryRequest) (*models.QuestionQueryResponse, error) {
	query, args := r.buildQuestionQuery(req)

	r.logger.WithFields(logrus.Fields{
		"skill_ids":            req.SkillIDs,
		"target_difficulty":    req.TargetDifficulty,
		"difficulty_range":     req.Diff<PERSON>,
		"exclude_question_ids": req.ExcludeQuestionIDs,
		"query":                query,
	}).Info("Executing question query")

	row := r.db.QueryRowContext(ctx, query, args...)

	var response models.QuestionQueryResponse
	var skillIDsArray pq.StringArray
	var assetURLsArray pq.StringArray
	var explanation sql.NullString

	err := row.Scan(
		&response.QuestionVersionID,
		&response.QuestionText,
		&response.Difficulty,
		&skillIDsArray,
		&assetURLsArray,
		&explanation,
		&response.Metadata,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			r.logger.WithFields(logrus.Fields{
				"skill_ids":            req.SkillIDs,
				"target_difficulty":    req.TargetDifficulty,
				"exclude_question_ids": req.ExcludeQuestionIDs,
			}).Warn("No question found matching criteria")
			return nil, fmt.Errorf("no question found matching criteria")
		}
		return nil, fmt.Errorf("failed to scan question: %w", err)
	}

	// Convert skill IDs from string array to UUID array
	response.SkillIDs = make([]uuid.UUID, len(skillIDsArray))
	for i, skillIDStr := range skillIDsArray {
		skillID, err := uuid.Parse(skillIDStr)
		if err != nil {
			r.logger.WithError(err).WithField("skill_id", skillIDStr).Error("Failed to parse skill ID")
			continue
		}
		response.SkillIDs[i] = skillID
	}

	// Convert asset URLs
	response.AssetURLs = []string(assetURLsArray)

	// Handle explanation
	if explanation.Valid {
		response.Explanation = &explanation.String
	}

	// Extract options from metadata if they exist
	if response.Metadata != nil {
		if options, ok := response.Metadata["options"].([]interface{}); ok {
			response.Options = make([]string, len(options))
			for i, option := range options {
				if optStr, ok := option.(string); ok {
					response.Options[i] = optStr
				}
			}
		}
	}

	r.logger.WithFields(logrus.Fields{
		"question_version_id": response.QuestionVersionID,
		"difficulty":          response.Difficulty,
		"skill_count":         len(response.SkillIDs),
	}).Info("Question found and returned")

	return &response, nil
}

// buildQuestionQuery constructs the SQL query based on the request criteria
func (r *QuestionRepository) buildQuestionQuery(req *models.QuestionQueryRequest) (string, []interface{}) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	baseQuery := `
		SELECT DISTINCT
			qv.id,
			qv.text,
			COALESCE(irt.difficulty, 0.5) as difficulty,
			ARRAY_AGG(DISTINCT qsm.skill_id::text) as skill_ids,
			COALESCE(ARRAY_AGG(DISTINCT qa.asset_url) FILTER (WHERE qa.asset_url IS NOT NULL), '{}') as asset_urls,
			qv.metadata->>'explanation' as explanation,
			qv.metadata
		FROM content.question_versions qv
		LEFT JOIN content.question_skill_mapping qsm ON qsm.question_version_id = qv.id
		LEFT JOIN models.item_response_theory_parameters irt ON irt.question_version_id = qv.id AND irt.model_type = '2PL'
		LEFT JOIN content.question_assets qa ON qa.question_version_id = qv.id
	`

	// Always filter for active questions
	conditions = append(conditions, "qv.status = 'active'")

	// Skill filtering
	if len(req.SkillIDs) > 0 {
		skillPlaceholders := make([]string, len(req.SkillIDs))
		for i, skillID := range req.SkillIDs {
			skillPlaceholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, skillID)
			argIndex++
		}
		conditions = append(conditions, fmt.Sprintf("qsm.skill_id = ANY(ARRAY[%s])", strings.Join(skillPlaceholders, ",")))
	}

	// Difficulty filtering
	if req.TargetDifficulty != nil {
		// Use a range around the target difficulty (±0.1)
		difficultyRange := 0.1
		conditions = append(conditions, fmt.Sprintf("irt.difficulty BETWEEN $%d AND $%d", argIndex, argIndex+1))
		args = append(args, *req.TargetDifficulty-difficultyRange, *req.TargetDifficulty+difficultyRange)
		argIndex += 2
	} else if req.DifficultyRange != nil {
		if req.DifficultyRange.Min != nil {
			conditions = append(conditions, fmt.Sprintf("irt.difficulty >= $%d", argIndex))
			args = append(args, *req.DifficultyRange.Min)
			argIndex++
		}
		if req.DifficultyRange.Max != nil {
			conditions = append(conditions, fmt.Sprintf("irt.difficulty <= $%d", argIndex))
			args = append(args, *req.DifficultyRange.Max)
			argIndex++
		}
	}

	// Exclude specific questions
	if len(req.ExcludeQuestionIDs) > 0 {
		excludePlaceholders := make([]string, len(req.ExcludeQuestionIDs))
		for i, questionID := range req.ExcludeQuestionIDs {
			excludePlaceholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, questionID)
			argIndex++
		}
		conditions = append(conditions, fmt.Sprintf("qv.id NOT IN (%s)", strings.Join(excludePlaceholders, ",")))
	}

	// Build the complete query
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	query := fmt.Sprintf(`
		%s
		%s
		GROUP BY qv.id, qv.text, qv.metadata, irt.difficulty
		ORDER BY RANDOM()
		LIMIT 1
	`, baseQuery, whereClause)

	return query, args
}

// GetQuestionByID retrieves a question by its ID with all related information
func (r *QuestionRepository) GetQuestionByID(ctx context.Context, questionID uuid.UUID) (*models.QuestionDetailResponse, error) {
	// Get the question
	questionQuery := `
		SELECT id, subject_id, title, created_at
		FROM content.questions
		WHERE id = $1
	`

	var question models.Question
	var subjectID sql.NullString
	var title sql.NullString

	err := r.db.QueryRowContext(ctx, questionQuery, questionID).Scan(
		&question.ID,
		&subjectID,
		&title,
		&question.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("question not found")
		}
		return nil, fmt.Errorf("failed to get question: %w", err)
	}

	if subjectID.Valid {
		subjectUUID, _ := uuid.Parse(subjectID.String)
		question.SubjectID = &subjectUUID
	}
	if title.Valid {
		question.Title = &title.String
	}

	// Get current active version
	versionQuery := `
		SELECT id, question_id, version_number, status, text, metadata, created_at
		FROM content.question_versions
		WHERE question_id = $1 AND status = 'active'
		ORDER BY version_number DESC
		LIMIT 1
	`

	var currentVersion models.QuestionVersion
	err = r.db.QueryRowContext(ctx, versionQuery, questionID).Scan(
		&currentVersion.ID,
		&currentVersion.QuestionID,
		&currentVersion.VersionNumber,
		&currentVersion.Status,
		&currentVersion.Text,
		&currentVersion.Metadata,
		&currentVersion.CreatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get current version: %w", err)
	}

	// Get all versions
	allVersionsQuery := `
		SELECT id, question_id, version_number, status, text, metadata, created_at
		FROM content.question_versions
		WHERE question_id = $1
		ORDER BY version_number DESC
	`

	rows, err := r.db.QueryContext(ctx, allVersionsQuery, questionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get all versions: %w", err)
	}
	defer rows.Close()

	var allVersions []models.QuestionVersion
	for rows.Next() {
		var version models.QuestionVersion
		err := rows.Scan(
			&version.ID,
			&version.QuestionID,
			&version.VersionNumber,
			&version.Status,
			&version.Text,
			&version.Metadata,
			&version.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan version: %w", err)
		}
		allVersions = append(allVersions, version)
	}

	// Get associated skills
	skillsQuery := `
		SELECT s.id, s.skill_domain_id, s.name, s.description
		FROM content.skills s
		JOIN content.question_skill_mapping qsm ON qsm.skill_id = s.id
		WHERE qsm.question_version_id = $1
	`

	skillRows, err := r.db.QueryContext(ctx, skillsQuery, currentVersion.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get skills: %w", err)
	}
	defer skillRows.Close()

	var skills []models.Skill
	for skillRows.Next() {
		var skill models.Skill
		var description sql.NullString
		err := skillRows.Scan(
			&skill.ID,
			&skill.SkillDomainID,
			&skill.Name,
			&description,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan skill: %w", err)
		}
		if description.Valid {
			skill.Description = &description.String
		}
		skills = append(skills, skill)
	}

	// Get assets
	assetsQuery := `
		SELECT id, question_version_id, asset_type, asset_url, description
		FROM content.question_assets
		WHERE question_version_id = $1
	`

	assetRows, err := r.db.QueryContext(ctx, assetsQuery, currentVersion.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get assets: %w", err)
	}
	defer assetRows.Close()

	var assets []models.QuestionAsset
	for assetRows.Next() {
		var asset models.QuestionAsset
		var assetType, description sql.NullString
		err := assetRows.Scan(
			&asset.ID,
			&asset.QuestionVersionID,
			&assetType,
			&asset.AssetURL,
			&description,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan asset: %w", err)
		}
		if assetType.Valid {
			asset.AssetType = &assetType.String
		}
		if description.Valid {
			asset.Description = &description.String
		}
		assets = append(assets, asset)
	}

	// Get IRT parameters
	irtQuery := `
		SELECT id, question_version_id, model_type, difficulty, discrimination, guessing, creation_date
		FROM models.item_response_theory_parameters
		WHERE question_version_id = $1 AND model_type = '2PL'
		LIMIT 1
	`

	var irtParams models.IRTParameters
	var discrimination, guessing sql.NullFloat64
	err = r.db.QueryRowContext(ctx, irtQuery, currentVersion.ID).Scan(
		&irtParams.ID,
		&irtParams.QuestionVersionID,
		&irtParams.ModelType,
		&irtParams.Difficulty,
		&discrimination,
		&guessing,
		&irtParams.CreationDate,
	)

	var irtParamsPtr *models.IRTParameters
	if err == nil {
		if discrimination.Valid {
			irtParams.Discrimination = &discrimination.Float64
		}
		if guessing.Valid {
			irtParams.Guessing = &guessing.Float64
		}
		irtParamsPtr = &irtParams
	} else if err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to get IRT parameters: %w", err)
	}

	response := &models.QuestionDetailResponse{
		Question:       question,
		CurrentVersion: currentVersion,
		AllVersions:    allVersions,
		Skills:         skills,
		Assets:         assets,
		IRTParameters:  irtParamsPtr,
	}

	return response, nil
}
