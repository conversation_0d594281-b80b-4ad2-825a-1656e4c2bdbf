package repository

import (
	"context"
	"database/sql"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"question-generation-service/internal/models"
)

func TestQuestionRepository_FindQuestion(t *testing.T) {
	// Create mock database
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	logger := logrus.New()
	dbWrapper := newTestDB(db, logger)
	repo := NewQuestionRepository(dbWrapper, logger)

	questionID := uuid.New()
	skillID1 := uuid.New()
	skillID2 := uuid.New()

	tests := []struct {
		name        string
		request     *models.QuestionQueryRequest
		setupMock   func()
		expectedErr string
		validate    func(*models.QuestionQueryResponse)
	}{
		{
			name: "successful query with skill filtering",
			request: &models.QuestionQueryRequest{
				SkillIDs:         []uuid.UUID{skillID1, skillID2},
				TargetDifficulty: func() *float64 { f := 0.5; return &f }(),
			},
			setupMock: func() {
				rows := sqlmock.NewRows([]string{
					"id", "text", "difficulty", "skill_ids", "asset_urls", "explanation", "metadata",
				}).AddRow(
					questionID.String(),
					"What is 2+2?",
					0.5,
					`{`+skillID1.String()+`,`+skillID2.String()+`}`,
					`{https://example.com/image.png}`,
					"Basic addition",
					`{"type":"multiple_choice","options":["3","4","5","6"],"correct_answer":"4"}`,
				)

				mock.ExpectQuery(`SELECT DISTINCT qv\.id, qv\.text, COALESCE\(irt\.difficulty, 0\.5\) as difficulty`).
					WithArgs(skillID1, skillID2, 0.4, 0.6).
					WillReturnRows(rows)
			},
			validate: func(resp *models.QuestionQueryResponse) {
				assert.Equal(t, questionID, resp.QuestionVersionID)
				assert.Equal(t, "What is 2+2?", resp.QuestionText)
				assert.Equal(t, 0.5, resp.Difficulty)
				assert.Contains(t, resp.SkillIDs, skillID1)
				assert.Contains(t, resp.SkillIDs, skillID2)
				assert.Contains(t, resp.AssetURLs, "https://example.com/image.png")
				assert.NotNil(t, resp.Explanation)
				assert.Equal(t, "Basic addition", *resp.Explanation)
				assert.NotNil(t, resp.Metadata)
			},
		},
		{
			name: "no questions found",
			request: &models.QuestionQueryRequest{
				SkillIDs: []uuid.UUID{skillID1},
			},
			setupMock: func() {
				mock.ExpectQuery(`SELECT DISTINCT qv\.id, qv\.text, COALESCE\(irt\.difficulty, 0\.5\) as difficulty`).
					WithArgs(skillID1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "text", "difficulty", "skill_ids", "asset_urls", "explanation", "metadata"}))
			},
			expectedErr: "no question found matching criteria",
		},
		{
			name: "database error",
			request: &models.QuestionQueryRequest{
				SkillIDs: []uuid.UUID{skillID1},
			},
			setupMock: func() {
				mock.ExpectQuery(`SELECT DISTINCT qv\.id, qv\.text, COALESCE\(irt\.difficulty, 0\.5\) as difficulty`).
					WithArgs(skillID1).
					WillReturnError(sql.ErrConnDone)
			},
			expectedErr: "failed to scan question",
		},
		{
			name: "query with difficulty range",
			request: &models.QuestionQueryRequest{
				SkillIDs: []uuid.UUID{skillID1},
				DifficultyRange: &models.DifficultyRange{
					Min: func() *float64 { f := 0.3; return &f }(),
					Max: func() *float64 { f := 0.7; return &f }(),
				},
			},
			setupMock: func() {
				rows := sqlmock.NewRows([]string{
					"id", "text", "difficulty", "skill_ids", "asset_urls", "explanation", "metadata",
				}).AddRow(
					questionID.String(),
					"What is 3+3?",
					0.6,
					`{`+skillID1.String()+`}`,
					`{}`,
					"",
					`{"type":"multiple_choice"}`,
				)

				mock.ExpectQuery(`SELECT DISTINCT qv\.id, qv\.text, COALESCE\(irt\.difficulty, 0\.5\) as difficulty`).
					WithArgs(skillID1, 0.3, 0.7).
					WillReturnRows(rows)
			},
			validate: func(resp *models.QuestionQueryResponse) {
				assert.Equal(t, questionID, resp.QuestionVersionID)
				assert.Equal(t, "What is 3+3?", resp.QuestionText)
				assert.Equal(t, 0.6, resp.Difficulty)
			},
		},
		{
			name: "query with exclusion list",
			request: &models.QuestionQueryRequest{
				SkillIDs:           []uuid.UUID{skillID1},
				ExcludeQuestionIDs: []uuid.UUID{uuid.New(), uuid.New()},
			},
			setupMock: func() {
				rows := sqlmock.NewRows([]string{
					"id", "text", "difficulty", "skill_ids", "asset_urls", "explanation", "metadata",
				}).AddRow(
					questionID.String(),
					"What is 4+4?",
					0.4,
					`{`+skillID1.String()+`}`,
					`{}`,
					"",
					`{"type":"multiple_choice"}`,
				)

				mock.ExpectQuery(`SELECT DISTINCT qv\.id, qv\.text, COALESCE\(irt\.difficulty, 0\.5\) as difficulty`).
					WillReturnRows(rows)
			},
			validate: func(resp *models.QuestionQueryResponse) {
				assert.Equal(t, questionID, resp.QuestionVersionID)
				assert.Equal(t, "What is 4+4?", resp.QuestionText)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()

			ctx := context.Background()
			resp, err := repo.FindQuestion(ctx, tt.request)

			if tt.expectedErr != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr)
				assert.Nil(t, resp)
			} else {
				if err != nil {
					t.Logf("Unexpected error: %v", err)
				}
				assert.NoError(t, err)
				if resp == nil {
					t.Logf("Response is nil")
				}
				assert.NotNil(t, resp)
				if tt.validate != nil && resp != nil {
					tt.validate(resp)
				}
			}

			// Verify all expectations were met
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// TODO: Add comprehensive tests for GetQuestionByID
// This method makes multiple database queries and requires complex mocking
// For now, we'll test it through integration tests
