package errors

import (
	"fmt"
	"net/http"
)

// ServiceError represents a service-specific error with HTTP status code
type ServiceError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Err     error  `json:"-"`
}

func (e *ServiceError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Err)
	}
	return e.Message
}

func (e *ServiceError) Unwrap() error {
	return e.Err
}

// Predefined error types
var (
	// Validation errors
	ErrInvalidRequest = &ServiceError{
		Code:    http.StatusBadRequest,
		Message: "Invalid request",
	}
	ErrMissingRequiredField = &ServiceError{
		Code:    http.StatusBadRequest,
		Message: "Missing required field",
	}
	ErrInvalidUUID = &ServiceError{
		Code:    http.StatusBadRequest,
		Message: "Invalid UUID format",
	}
	ErrInvalidDifficulty = &ServiceError{
		Code:    http.StatusBadRequest,
		Message: "Difficulty must be between -10.0 and 10.0",
	}

	// Not found errors
	ErrQuestionNotFound = &ServiceError{
		Code:    http.StatusNotFound,
		Message: "Question not found",
	}
	ErrSkillNotFound = &ServiceError{
		Code:    http.StatusNotFound,
		Message: "Skill not found",
	}
	ErrNoQuestionsAvailable = &ServiceError{
		Code:    http.StatusNotFound,
		Message: "No questions available matching the specified criteria",
	}

	// Database errors
	ErrDatabaseConnection = &ServiceError{
		Code:    http.StatusInternalServerError,
		Message: "Database connection error",
	}
	ErrDatabaseQuery = &ServiceError{
		Code:    http.StatusInternalServerError,
		Message: "Database query error",
	}
	ErrTransactionFailed = &ServiceError{
		Code:    http.StatusInternalServerError,
		Message: "Database transaction failed",
	}

	// Internal errors
	ErrInternalServer = &ServiceError{
		Code:    http.StatusInternalServerError,
		Message: "Internal server error",
	}
	ErrServiceUnavailable = &ServiceError{
		Code:    http.StatusServiceUnavailable,
		Message: "Service temporarily unavailable",
	}
)

// NewValidationError creates a new validation error
func NewValidationError(message string) *ServiceError {
	return &ServiceError{
		Code:    http.StatusBadRequest,
		Message: message,
	}
}

// NewNotFoundError creates a new not found error
func NewNotFoundError(message string) *ServiceError {
	return &ServiceError{
		Code:    http.StatusNotFound,
		Message: message,
	}
}

// NewDatabaseError creates a new database error
func NewDatabaseError(message string, err error) *ServiceError {
	return &ServiceError{
		Code:    http.StatusInternalServerError,
		Message: message,
		Err:     err,
	}
}

// NewInternalError creates a new internal server error
func NewInternalError(message string, err error) *ServiceError {
	return &ServiceError{
		Code:    http.StatusInternalServerError,
		Message: message,
		Err:     err,
	}
}

// WrapError wraps an existing error with a service error
func WrapError(err error, code int, message string) *ServiceError {
	return &ServiceError{
		Code:    code,
		Message: message,
		Err:     err,
	}
}

// IsServiceError checks if an error is a ServiceError
func IsServiceError(err error) (*ServiceError, bool) {
	if serviceErr, ok := err.(*ServiceError); ok {
		return serviceErr, true
	}
	return nil, false
}

// GetHTTPStatusCode returns the HTTP status code for an error
func GetHTTPStatusCode(err error) int {
	if serviceErr, ok := IsServiceError(err); ok {
		return serviceErr.Code
	}
	return http.StatusInternalServerError
}

// GetErrorMessage returns a user-friendly error message
func GetErrorMessage(err error) string {
	if serviceErr, ok := IsServiceError(err); ok {
		return serviceErr.Message
	}
	return "An unexpected error occurred"
}
