package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// QuestionStatus represents the status of a question version
type QuestionStatus string

const (
	QuestionStatusDraft    QuestionStatus = "draft"
	QuestionStatusActive   QuestionStatus = "active"
	QuestionStatusArchived QuestionStatus = "archived"
)

// IRTModelType represents the type of IRT model
type IRTModelType string

const (
	IRTModel1PL IRTModelType = "1PL"
	IRTModel2PL IRTModelType = "2PL"
	IRTModel3PL IRTModelType = "3PL"
)

// Subject represents a subject in the content schema
type Subject struct {
	ID          uuid.UUID `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description *string   `json:"description,omitempty" db:"description"`
}

// SkillDomain represents a skill domain in the content schema
type SkillDomain struct {
	ID          uuid.UUID `json:"id" db:"id"`
	SubjectID   uuid.UUID `json:"subject_id" db:"subject_id"`
	Name        string    `json:"name" db:"name"`
	Description *string   `json:"description,omitempty" db:"description"`
}

// Skill represents a skill in the content schema
type Skill struct {
	ID            uuid.UUID `json:"id" db:"id"`
	SkillDomainID uuid.UUID `json:"skill_domain_id" db:"skill_domain_id"`
	Name          string    `json:"name" db:"name"`
	Description   *string   `json:"description,omitempty" db:"description"`
}

// Question represents a question in the content schema
type Question struct {
	ID        uuid.UUID  `json:"id" db:"id"`
	SubjectID *uuid.UUID `json:"subject_id,omitempty" db:"subject_id"`
	Title     *string    `json:"title,omitempty" db:"title"`
	CreatedAt time.Time  `json:"created_at" db:"created_at"`
}

// QuestionMetadata represents the JSONB metadata for a question version
type QuestionMetadata map[string]interface{}

// Value implements the driver.Valuer interface for database storage
func (qm QuestionMetadata) Value() (driver.Value, error) {
	if qm == nil {
		return nil, nil
	}
	return json.Marshal(qm)
}

// Scan implements the sql.Scanner interface for database retrieval
func (qm *QuestionMetadata) Scan(value interface{}) error {
	if value == nil {
		*qm = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, qm)
}

// QuestionVersion represents a version of a question
type QuestionVersion struct {
	ID            uuid.UUID        `json:"id" db:"id"`
	QuestionID    uuid.UUID        `json:"question_id" db:"question_id"`
	VersionNumber int              `json:"version_number" db:"version_number"`
	Status        QuestionStatus   `json:"status" db:"status"`
	Text          string           `json:"text" db:"text"`
	Metadata      QuestionMetadata `json:"metadata,omitempty" db:"metadata"`
	CreatedAt     time.Time        `json:"created_at" db:"created_at"`
}

// QuestionAsset represents an asset associated with a question version
type QuestionAsset struct {
	ID                uuid.UUID `json:"id" db:"id"`
	QuestionVersionID uuid.UUID `json:"question_version_id" db:"question_version_id"`
	AssetType         *string   `json:"asset_type,omitempty" db:"asset_type"`
	AssetURL          string    `json:"asset_url" db:"asset_url"`
	Description       *string   `json:"description,omitempty" db:"description"`
}

// IRTParameters represents IRT model parameters for a question
type IRTParameters struct {
	ID                uuid.UUID    `json:"id" db:"id"`
	QuestionVersionID uuid.UUID    `json:"question_version_id" db:"question_version_id"`
	ModelType         IRTModelType `json:"model_type" db:"model_type"`
	Difficulty        float64      `json:"difficulty" db:"difficulty"`
	Discrimination    *float64     `json:"discrimination,omitempty" db:"discrimination"`
	Guessing          *float64     `json:"guessing,omitempty" db:"guessing"`
	CreationDate      time.Time    `json:"creation_date" db:"creation_date"`
}
