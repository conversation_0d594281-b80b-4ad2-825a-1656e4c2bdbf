package models

import "github.com/google/uuid"

// DifficultyRange represents a range of difficulty values
type DifficultyRange struct {
	Min *float64 `json:"min,omitempty"`
	Max *float64 `json:"max,omitempty"`
}

// QuestionQueryRequest represents the request for querying questions
type QuestionQueryRequest struct {
	SkillIDs            []uuid.UUID      `json:"skill_ids,omitempty"`
	TargetDifficulty    *float64         `json:"target_difficulty,omitempty"`
	DifficultyRange     *DifficultyRange `json:"difficulty_range,omitempty"`
	ExcludeQuestionIDs  []uuid.UUID      `json:"exclude_question_ids,omitempty"`
	QuestionType        *string          `json:"question_type,omitempty"`
	Strategy            *string          `json:"strategy,omitempty"`
}

// QuestionQueryResponse represents the response for a question query
type QuestionQueryResponse struct {
	QuestionVersionID uuid.UUID        `json:"question_version_id"`
	QuestionText      string           `json:"question_text"`
	Options           []string         `json:"options,omitempty"`
	SkillIDs          []uuid.UUID      `json:"skill_ids"`
	Difficulty        float64          `json:"difficulty"`
	AssetURLs         []string         `json:"asset_urls,omitempty"`
	Explanation       *string          `json:"explanation,omitempty"`
	Metadata          QuestionMetadata `json:"metadata,omitempty"`
}

// CreateQuestionRequest represents the request for creating a new question
type CreateQuestionRequest struct {
	SubjectID   *uuid.UUID       `json:"subject_id,omitempty"`
	Title       *string          `json:"title,omitempty"`
	Text        string           `json:"text" validate:"required"`
	SkillIDs    []uuid.UUID      `json:"skill_ids" validate:"required,min=1"`
	Metadata    QuestionMetadata `json:"metadata,omitempty"`
	Difficulty  *float64         `json:"difficulty,omitempty"`
	AssetURLs   []string         `json:"asset_urls,omitempty"`
}

// CreateQuestionResponse represents the response for creating a question
type CreateQuestionResponse struct {
	QuestionID        uuid.UUID `json:"question_id"`
	QuestionVersionID uuid.UUID `json:"question_version_id"`
	Message           string    `json:"message"`
}

// CreateQuestionVersionRequest represents the request for creating a new question version
type CreateQuestionVersionRequest struct {
	Text       string           `json:"text" validate:"required"`
	Metadata   QuestionMetadata `json:"metadata,omitempty"`
	Difficulty *float64         `json:"difficulty,omitempty"`
	AssetURLs  []string         `json:"asset_urls,omitempty"`
}

// CreateQuestionVersionResponse represents the response for creating a question version
type CreateQuestionVersionResponse struct {
	QuestionVersionID uuid.UUID `json:"question_version_id"`
	VersionNumber     int       `json:"version_number"`
	Message           string    `json:"message"`
}

// CreateSkillRequest represents the request for creating a new skill
type CreateSkillRequest struct {
	SkillDomainID uuid.UUID `json:"skill_domain_id" validate:"required"`
	Name          string    `json:"name" validate:"required"`
	Description   *string   `json:"description,omitempty"`
}

// CreateSkillResponse represents the response for creating a skill
type CreateSkillResponse struct {
	SkillID uuid.UUID `json:"skill_id"`
	Message string    `json:"message"`
}

// QuestionDetailResponse represents detailed information about a question
type QuestionDetailResponse struct {
	Question         Question          `json:"question"`
	CurrentVersion   QuestionVersion   `json:"current_version"`
	AllVersions      []QuestionVersion `json:"all_versions,omitempty"`
	Skills           []Skill           `json:"skills"`
	Assets           []QuestionAsset   `json:"assets,omitempty"`
	IRTParameters    *IRTParameters    `json:"irt_parameters,omitempty"`
}

// SkillListResponse represents a list of skills with optional filtering
type SkillListResponse struct {
	Skills []SkillWithDomain `json:"skills"`
	Total  int               `json:"total"`
}

// SkillWithDomain represents a skill with its domain and subject information
type SkillWithDomain struct {
	Skill       Skill       `json:"skill"`
	SkillDomain SkillDomain `json:"skill_domain"`
	Subject     Subject     `json:"subject"`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message,omitempty"`
	Code    int    `json:"code,omitempty"`
}

// HealthResponse represents a health check response
type HealthResponse struct {
	Status    string `json:"status"`
	Timestamp string `json:"timestamp"`
	Service   string `json:"service"`
}
