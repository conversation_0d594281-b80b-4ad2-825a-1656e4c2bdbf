package config

import (
	"os"
	"strconv"
	"time"
)

// Config holds all configuration for the question generation service
type Config struct {
	// Server configuration
	Port string

	// Database configuration
	DatabaseURL string

	// HTTP client timeouts
	HTTPClientTimeout time.Duration

	// Database connection pool settings
	MaxOpenConns    int
	MaxIdleConns    int
	ConnMaxLifetime time.Duration

	// Logging configuration
	LogLevel string

	// Service information
	ServiceName    string
	ServiceVersion string
}

// Load reads configuration from environment variables
func Load() *Config {
	config := &Config{
		Port:              getEnv("PORT", "8082"),
		DatabaseURL:       getEnv("DB_CONN_STRING", "postgres://localhost/orchestrator?sslmode=disable"),
		HTTPClientTimeout: getDurationEnv("HTTP_CLIENT_TIMEOUT", 30*time.Second),
		MaxOpenConns:      getIntEnv("DB_MAX_OPEN_CONNS", 25),
		MaxIdleConns:      getIntEnv("DB_MAX_IDLE_CONNS", 5),
		ConnMaxLifetime:   getDurationEnv("DB_CONN_MAX_LIFETIME", 5*time.Minute),
		LogLevel:          getEnv("LOG_LEVEL", "info"),
		ServiceName:       getEnv("SERVICE_NAME", "question-generation-service"),
		ServiceVersion:    getEnv("SERVICE_VERSION", "1.0.0"),
	}

	return config
}

// getEnv gets an environment variable with a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getDurationEnv gets a duration environment variable with a default value
func getDurationEnv(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

// getIntEnv gets an integer environment variable with a default value
func getIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getBoolEnv gets a boolean environment variable with a default value
func getBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}
